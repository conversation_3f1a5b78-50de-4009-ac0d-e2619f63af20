<?php

namespace App\Services\Time;

use App\Models\Customer\Configuration;
use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use App\Models\Customer\Time\TimeRequest\TimeRequest;
use App\Models\Customer\Employee;
use App\Models\Customer\EmployeeDtl;
use App\Services\ConnectionService;
use App\Filament\Components\ChronoAnchorComponent;
use App\Filament\Components\ChronoCountComponent;
use Carbon\Carbon;

class TimeRequestDateConstraintService
{
    /**
     * Get date constraints for a specific attendance status
     *
     * @param int $attStatus
     * @return array ['type' => string, 'min_date' => string|null, 'max_date' => string|null, 'message' => string]
     */
    public function getDateConstraints($attStatus)
    {
        try {
            // Get configuration ID using existing EmployeeAttendanceDtl method
            $employeeAttendanceDtl = app(EmployeeAttendanceDtl::class);
            $attStatusId = $employeeAttendanceDtl->getAttStatusId($attStatus);

            if (!$attStatusId) {
                return ['type' => 'none', 'min_date' => null, 'max_date' => null, 'message' => ''];
            }

            // Handle different attendance status types
            switch ($attStatusId) {
                case 'ATTENDANCE_STATUS_VACATION':
                    return $this->getVacationDateConstraints($attStatusId);
                    
                case 'ATTENDANCE_STATUS_OUT_WORK':
                case 'ATTENDANCE_STATUS_FURLOUGH.ATTENDANCE_STATUS_FURLOUGH_LONG':
                    return $this->getValue1DateConstraints($attStatusId);
                    
                case 'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY':
                case 'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_KID_BIRTHDAY':
                case 'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_HALFDAY':
                case 'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE':
                    return $this->getSalaryFurloughDateConstraints($attStatusId);
                    
                default:
                    // For other statuses, try value1 configuration
                    return $this->getValue1DateConstraints($attStatusId);
            }

        } catch (\Exception $e) {
            \Log::error('TimeRequestDateConstraintService error: ' . $e->getMessage());
            return ['type' => 'none', 'min_date' => null, 'max_date' => null, 'message' => ''];
        }
    }



    /**
     * Get date constraints for vacation requests
     * Max date of (employee company_work_date + shift) and (value1 now date + shift)
     */
    private function getVacationDateConstraints($attStatusId)
    {
        \Log::info('TimeRequestDateConstraintService::getVacationDateConstraints - Start', [
            'attStatusId' => $attStatusId
        ]);

        $cn = ConnectionService::getCNForEmployeeUser();

        // Get vacation configuration using Eloquent
        $config = Configuration::on($cn)
            ->where(Configuration::CONFIG_ID, 'ATTENDANCE_STATUS_VACATION')
            ->first();

        \Log::info('TimeRequestDateConstraintService::getVacationDateConstraints - Configuration loaded', [
            'config_found' => !is_null($config),
            'config_value' => $config ? $config->value : null,
            'config_value1' => $config ? $config->value1 : null
        ]);

        if (!$config) {
            \Log::warning('TimeRequestDateConstraintService::getVacationDateConstraints - No configuration found');
            return ['type' => 'none', 'min_date' => null, 'max_date' => null, 'message' => ''];
        }

        $minDates = [];

        // Calculate minimum date from value (company work date + shift)
        if ($config->value) {
            $companyWorkDateMin = $this->calculateVacationCompanyWorkDateMin($config->value);
            if ($companyWorkDateMin) {
                $minDates[] = $companyWorkDateMin;
            }
        }

        // Calculate minimum date from value1 (now + shift)
        if ($config->value1) {
            $nowDateMin = $this->calculateAnchorMinDate($config->value1);
            if ($nowDateMin) {
                $minDates[] = $nowDateMin;
            }
        }

        // Calculate minimum date from last existing TimeRequest record (end_date + value shift)
        if ($config->value) {
            $existingRequestsMin = $this->calculateExistingVacationRequestsMin($config->value, $cn);
            if (!empty($existingRequestsMin)) {
                $minDates = array_merge($minDates, $existingRequestsMin);
            }
        }

        if (empty($minDates)) {
            return ['type' => 'none', 'min_date' => null, 'max_date' => null, 'message' => ''];
        }

        // Return the maximum (latest) of all minimum dates
        $maxMinDate = collect($minDates)->max();

        // Determine precision based on chrono types used in configuration
        $chronoTypes = [];
        if ($config->value) {
            $valueAnchorData = ChronoAnchorComponent::parseFormula($config->value);
            if ($valueAnchorData['type']) {
                $chronoTypes[] = $valueAnchorData['type'];
            }
        }
        if ($config->value1) {
            $value1AnchorData = ChronoAnchorComponent::parseFormula($config->value1);
            if ($value1AnchorData['type']) {
                $chronoTypes[] = $value1AnchorData['type'];
            }
        }

        // Use minute precision if any chrono type is less than day
        $useMinutePrecision = $this->shouldUseMinutePrecision($chronoTypes);
        $dateFormat = $useMinutePrecision ? 'Y-m-d H:i' : 'Y-m-d';

        $result = [
            'type' => 'min_date',
            'min_date' => $maxMinDate->format($dateFormat),
            'max_date' => null,
            'message' => 'Vacation requests must be after ' . $maxMinDate->format($dateFormat)
        ];

        \Log::info('TimeRequestDateConstraintService::getVacationDateConstraints - End', [
            'result' => $result,
            'min_dates_count' => count($minDates),
            'max_min_date' => $maxMinDate->format($dateFormat),
            'includes_existing_requests' => $config->value ? true : false
        ]);

        return $result;
    }

    /**
     * Get date constraints from value1 configuration only
     */
    private function getValue1DateConstraints($attStatusId)
    {
        \Log::info('TimeRequestDateConstraintService::getValue1DateConstraints - Start', [
            'attStatusId' => $attStatusId
        ]);

        $cn = ConnectionService::getCNForEmployeeUser();

        // For OUT_WORK and FURLOUGH_LONG, get configuration directly using Eloquent
        $config = Configuration::on($cn)
            ->where(Configuration::CONFIG_ID, $attStatusId)
            ->first();

        \Log::info('TimeRequestDateConstraintService::getValue1DateConstraints - Configuration loaded', [
            'config_found' => !is_null($config),
            'config_value1' => $config ? $config->value1 : null
        ]);

        if (!$config || !$config->value1) {
            \Log::warning('TimeRequestDateConstraintService::getValue1DateConstraints - No configuration or value1 found');
            return ['type' => 'none', 'min_date' => null, 'max_date' => null, 'message' => ''];
        }

        $minDate = $this->calculateAnchorMinDate($config->value1);

        \Log::info('TimeRequestDateConstraintService::getValue1DateConstraints - Min date calculated', [
            'min_date' => $minDate ? $minDate->format('Y-m-d') : null
        ]);

        if (!$minDate) {
            \Log::warning('TimeRequestDateConstraintService::getValue1DateConstraints - No min date calculated');
            return ['type' => 'none', 'min_date' => null, 'max_date' => null, 'message' => ''];
        }

        // Determine precision based on chrono type in value1 configuration
        $anchorData = ChronoAnchorComponent::parseFormula($config->value1);
        $useMinutePrecision = $this->shouldUseMinutePrecision([$anchorData['type']]);
        $dateFormat = $useMinutePrecision ? 'Y-m-d H:i' : 'Y-m-d';

        $result = [
            'type' => 'min_date',
            'min_date' => $minDate->format($dateFormat),
            'max_date' => null,
            'message' => 'Requests must be after ' . $minDate->format($dateFormat)
        ];

        \Log::info('TimeRequestDateConstraintService::getValue1DateConstraints - End', [
            'result' => $result
        ]);

        return $result;
    }

    /**
     * Get date constraints for salary furlough requests
     * Max of value and value1 valid dates
     */
    private function getSalaryFurloughDateConstraints($attStatusId)
    {
        \Log::info('TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Start', [
            'attStatusId' => $attStatusId
        ]);

        $cn = ConnectionService::getCNForEmployeeUser();

        // Get salary furlough configuration using Eloquent
        $config = Configuration::on($cn)
            ->where(Configuration::CONFIG_ID, $attStatusId)
            ->first();

        \Log::info('TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Configuration loaded', [
            'config_found' => !is_null($config),
            'config_value' => $config ? $config->value : null,
            'config_value1' => $config ? $config->value1 : null
        ]);

        if (!$config) {
            \Log::warning('TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - No configuration found');
            return ['type' => 'none', 'min_date' => null, 'max_date' => null, 'message' => ''];
        }

        $minDates = [];

        // Calculate minimum date from value1 (anchor-based)
        if ($config->value1) {
            $anchorMin = $this->calculateAnchorMinDate($config->value1);
            if ($anchorMin) {
                $minDates[] = $anchorMin;
            }
        }

        // Calculate minimum date from value (count-based session logic)
        if ($config->value) {
            // First check count limit validation for salary furlough subtypes
            $countLimitResult = $this->validateCountLimitForConstraints($config->value, $attStatusId);

            \Log::info('TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Count limit check', [
                'countLimitValid' => $countLimitResult['valid'],
                'message' => $countLimitResult['message']
            ]);

            // Calculate session-based minimum date based on count limit result
            // If count limit valid: use previous session end date + 1
            // If count limit invalid: use current session end date + 1
            $sessionMin = $this->calculateSessionMinDate($config->value, $attStatusId, $countLimitResult['valid']);
            if ($sessionMin) {
                $minDates[] = $sessionMin;
            }
        }

        \Log::info('TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Min dates calculated', [
            'min_dates_count' => count($minDates),
            'min_dates' => array_map(function($date) { return $date ? $date->format('Y-m-d') : null; }, $minDates)
        ]);

        if (empty($minDates)) {
            \Log::warning('TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - No min dates calculated');
            return ['type' => 'none', 'min_date' => null, 'max_date' => null, 'message' => ''];
        }

        // Return the maximum (latest) of all minimum dates
        $maxMinDate = collect($minDates)->max();

        // Determine precision based on chrono types used in configuration
        $chronoTypes = [];
        if ($config->value1) {
            $value1AnchorData = ChronoAnchorComponent::parseFormula($config->value1);
            if ($value1AnchorData['type']) {
                $chronoTypes[] = $value1AnchorData['type'];
            }
        }
        if ($config->value) {
            $valueCountData = ChronoCountComponent::parseFormula($config->value);
            if ($valueCountData['static_type']) {
                $chronoTypes[] = $valueCountData['static_type'];
            }
        }

        // Use minute precision if any chrono type is less than day
        $useMinutePrecision = $this->shouldUseMinutePrecision($chronoTypes);
        $dateFormat = $useMinutePrecision ? 'Y-m-d H:i' : 'Y-m-d';

        $result = [
            'type' => 'min_date',
            'min_date' => $maxMinDate->format($dateFormat),
            'max_date' => null,
            'message' => 'Salary furlough requests must be after ' . $maxMinDate->format($dateFormat)
        ];

        \Log::info('TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - End', [
            'result' => $result,
            'max_min_date' => $maxMinDate->format('Y-m-d')
        ]);

        return $result;
    }

    /**
     * Calculate minimum date from anchor configuration
     */
    private function calculateAnchorMinDate($anchorFormula)
    {
        \Log::info('TimeRequestDateConstraintService::calculateAnchorMinDate - Start', [
            'anchorFormula' => $anchorFormula
        ]);

        try {
            $anchorData = ChronoAnchorComponent::parseFormula($anchorFormula);

            \Log::info('TimeRequestDateConstraintService::calculateAnchorMinDate - Formula parsed', [
                'anchorData' => $anchorData
            ]);

            if (!$anchorData['type'] || $anchorData['shift_count'] === null) {
                \Log::warning('TimeRequestDateConstraintService::calculateAnchorMinDate - Invalid anchor data', [
                    'type' => $anchorData['type'] ?? null,
                    'shift_count' => $anchorData['shift_count'] ?? null
                ]);
                return null;
            }

            // Use the same logic as TimeRequestValidationService
            $validationService = app(TimeRequestValidationService::class);
            $reflection = new \ReflectionClass($validationService);
            $method = $reflection->getMethod('calculateMinimumDate');
            $method->setAccessible(true);

            $result = $method->invoke($validationService, $anchorData);

            \Log::info('TimeRequestDateConstraintService::calculateAnchorMinDate - End', [
                'result' => $result ? $result->format('Y-m-d H:i:s') : null,
                'chrono_type' => $anchorData['type']
            ]);

            return $result;

        } catch (\Exception $e) {
            \Log::error('TimeRequestDateConstraintService::calculateAnchorMinDate - Error', [
                'error' => $e->getMessage(),
                'anchorFormula' => $anchorFormula
            ]);
            return null;
        }
    }

    /**
     * Calculate minimum date from count configuration (current session end date + 1 day)
     * For salary furlough subtypes, uses static_type instead of chrono_type
     */
    private function calculateCountMinDate($countFormula, $attStatusId = null)
    {
        \Log::info('TimeRequestDateConstraintService::calculateCountMinDate - Start', [
            'countFormula' => $countFormula,
            'attStatusId' => $attStatusId
        ]);

        try {
            $countData = ChronoCountComponent::parseFormula($countFormula);

            \Log::info('TimeRequestDateConstraintService::calculateCountMinDate - Formula parsed', [
                'countData' => $countData
            ]);

            // Determine which type to use based on attendance status
            $isSalaryFurloughSubtype = $attStatusId && str_contains($attStatusId, 'ATTENDANCE_STATUS_SALARY_FURLOUGH.');
            $typeToUse = $isSalaryFurloughSubtype ? $countData['static_type'] : $countData['chrono_type'];

            \Log::info('TimeRequestDateConstraintService::calculateCountMinDate - Type selection', [
                'isSalaryFurloughSubtype' => $isSalaryFurloughSubtype,
                'static_type' => $countData['static_type'],
                'chrono_type' => $countData['chrono_type'],
                'typeToUse' => $typeToUse
            ]);

            if (!$typeToUse) {
                \Log::warning('TimeRequestDateConstraintService::calculateCountMinDate - No type found', [
                    'static_type' => $countData['static_type'],
                    'chrono_type' => $countData['chrono_type']
                ]);
                return null;
            }

            $now = Carbon::now();

            \Log::info('TimeRequestDateConstraintService::calculateCountMinDate - Processing', [
                'now' => $now->format('Y-m-d H:i:s'),
                'typeToUse' => $typeToUse
            ]);

            // Use the same logic as TimeRequestValidationService
            $validationService = app(TimeRequestValidationService::class);
            $reflection = new \ReflectionClass($validationService);
            $method = $reflection->getMethod('getCurrentSessionEndDate');
            $method->setAccessible(true);

            $sessionEndDate = $method->invoke($validationService, $now, $typeToUse);

            \Log::info('TimeRequestDateConstraintService::calculateCountMinDate - Session end date calculated', [
                'sessionEndDate' => $sessionEndDate ? $sessionEndDate->format('Y-m-d H:i:s') : null
            ]);

            if (!$sessionEndDate) {
                \Log::warning('TimeRequestDateConstraintService::calculateCountMinDate - No session end date calculated');
                return null;
            }

            // Return the day after the current session ends
            $result = $sessionEndDate->addDay();

            \Log::info('TimeRequestDateConstraintService::calculateCountMinDate - End', [
                'result' => $result->format('Y-m-d H:i:s')
            ]);

            return $result;

        } catch (\Exception $e) {
            \Log::error('TimeRequestDateConstraintService::calculateCountMinDate - Error', [
                'error' => $e->getMessage(),
                'countFormula' => $countFormula
            ]);
            return null;
        }
    }

    /**
     * Calculate minimum date for vacation based on company work date
     */
    private function calculateVacationCompanyWorkDateMin($anchorFormula)
    {
        \Log::info('TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Start', [
            'anchorFormula' => $anchorFormula
        ]);

        try {
            $anchorData = ChronoAnchorComponent::parseFormula($anchorFormula);

            \Log::info('TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Formula parsed', [
                'anchorData' => $anchorData
            ]);

            if (!$anchorData['type'] || $anchorData['shift_count'] === null ||
                $anchorData['date_source'] !== 'employee_dtl.company_work_date') {
                \Log::warning('TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Invalid anchor data', [
                    'type' => $anchorData['type'] ?? null,
                    'shift_count' => $anchorData['shift_count'] ?? null,
                    'date_source' => $anchorData['date_source'] ?? null
                ]);
                return null;
            }

            // Get current user's employee and employee_dtl
            $cn = ConnectionService::getCNForEmployeeUser();
            $user = auth()->user();

            \Log::info('TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - User info', [
                'user_id' => $user ? $user->id : null,
                'user_phone' => $user ? $user->phone : null
            ]);

            if (!$user) {
                \Log::warning('TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - No authenticated user');
                return null;
            }

            $employee = Employee::on($cn)->where(Employee::PHONE, $user->phone)->first();

            \Log::info('TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Employee info', [
                'employee_found' => !is_null($employee),
                'employee_id' => $employee ? $employee->id : null,
                'has_employee_dtl' => $employee && $employee->employee_dtl ? true : false,
                'company_work_date' => $employee && $employee->employee_dtl ? $employee->employee_dtl->company_work_date : null
            ]);

            if (!$employee || !$employee->employee_dtl || !$employee->employee_dtl->company_work_date) {
                \Log::warning('TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Missing employee data');
                return null;
            }

            $companyWorkDate = Carbon::parse($employee->employee_dtl->company_work_date);

            \Log::info('TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Company work date parsed', [
                'companyWorkDate' => $companyWorkDate->format('Y-m-d H:i:s'),
                'shift_type' => $anchorData['type'],
                'shift_count' => $anchorData['shift_count']
            ]);

            // Use the same logic as TimeRequestValidationService
            $validationService = app(TimeRequestValidationService::class);
            $reflection = new \ReflectionClass($validationService);
            $method = $reflection->getMethod('applyShiftToDate');
            $method->setAccessible(true);

            $result = $method->invoke($validationService, $companyWorkDate, $anchorData['type'], $anchorData['shift_count']);

            \Log::info('TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - End', [
                'result' => $result ? $result->format('Y-m-d H:i:s') : null
            ]);

            return $result;

        } catch (\Exception $e) {
            \Log::error('TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Error', [
                'error' => $e->getMessage(),
                'anchorFormula' => $anchorFormula
            ]);
            return null;
        }
    }

    /**
     * Calculate minimum date from the last existing vacation request
     * Gets the most recent vacation request by end_date and calculates: end_date + value shift
     */
    private function calculateExistingVacationRequestsMin($anchorFormula, $cn)
    {
        \Log::info('TimeRequestDateConstraintService::calculateExistingVacationRequestsMin - Start', [
            'anchorFormula' => $anchorFormula
        ]);

        try {
            $anchorData = ChronoAnchorComponent::parseFormula($anchorFormula);

            if (!$anchorData['type'] || $anchorData['shift_count'] === null) {
                \Log::warning('TimeRequestDateConstraintService::calculateExistingVacationRequestsMin - Invalid anchor data');
                return [];
            }

            // Get current user's employee
            $user = auth()->user();
            if (!$user) {
                \Log::warning('TimeRequestDateConstraintService::calculateExistingVacationRequestsMin - No authenticated user');
                return [];
            }

            $employee = Employee::on($cn)->where(Employee::PHONE, $user->phone)->first();
            if (!$employee) {
                \Log::warning('TimeRequestDateConstraintService::calculateExistingVacationRequestsMin - No employee found');
                return [];
            }

            // Get only the last vacation request by end_date for this employee
            $lastRequest = TimeRequest::on($cn)
                ->where(TimeRequest::EMPLOYEE_ID, $employee->id)
                ->where(TimeRequest::ATT_STATUS, EmployeeAttendanceDtl::ATTENDANCE_STATUS_VACATION)
                ->whereIn(TimeRequest::CONFIRM_STATUS, [
                    TimeRequest::CONFIRM_STATUS_SENT,
                    TimeRequest::CONFIRM_STATUS_CONFIRMED,
                    TimeRequest::CONFIRM_STATUS_TEMP_CONFIRMED
                ])
                ->whereNotNull(TimeRequest::END_DATE)
                ->orderBy(TimeRequest::END_DATE, 'desc')
                ->first();

            \Log::info('TimeRequestDateConstraintService::calculateExistingVacationRequestsMin - Last request found', [
                'request_found' => !is_null($lastRequest),
                'request_id' => $lastRequest ? $lastRequest->id : null,
                'end_date' => $lastRequest ? $lastRequest->end_date : null
            ]);

            $minDates = [];

            // Process only the last request if it exists
            if ($lastRequest && $lastRequest->end_date) {
                $endDate = Carbon::parse($lastRequest->end_date);

                // Use the same logic as TimeRequestValidationService
                $validationService = app(TimeRequestValidationService::class);
                $reflection = new \ReflectionClass($validationService);
                $method = $reflection->getMethod('applyShiftToDate');
                $method->setAccessible(true);

                $shiftedDate = $method->invoke($validationService, $endDate, $anchorData['type'], $anchorData['shift_count']);

                if ($shiftedDate) {
                    $minDates[] = $shiftedDate;

                    \Log::info('TimeRequestDateConstraintService::calculateExistingVacationRequestsMin - Last request processed', [
                        'request_id' => $lastRequest->id,
                        'end_date' => $endDate->format('Y-m-d'),
                        'shifted_date' => $shiftedDate->format('Y-m-d H:i:s')
                    ]);
                }
            }

            \Log::info('TimeRequestDateConstraintService::calculateExistingVacationRequestsMin - End', [
                'min_dates_count' => count($minDates),
                'processed_last_request_only' => true
            ]);

            return $minDates;

        } catch (\Exception $e) {
            \Log::error('TimeRequestDateConstraintService::calculateExistingVacationRequestsMin - Error', [
                'error' => $e->getMessage(),
                'anchorFormula' => $anchorFormula
            ]);
            return [];
        }
    }

    /**
     * Validate count limit for salary furlough constraints
     * Similar to TimeRequestValidationService::validateCountLimit but adapted for constraints
     *
     * @param string $countFormula
     * @param string $attStatusId
     * @return array ['valid' => bool, 'message' => string]
     */
    private function validateCountLimitForConstraints($countFormula, $attStatusId)
    {
        \Log::info('TimeRequestDateConstraintService::validateCountLimitForConstraints - Start', [
            'countFormula' => $countFormula,
            'attStatusId' => $attStatusId
        ]);

        try {
            $countData = ChronoCountComponent::parseFormula($countFormula);

            if (!$countData['static_type'] || $countData['count'] === null) {
                \Log::warning('TimeRequestDateConstraintService::validateCountLimitForConstraints - Invalid count data');
                return ['valid' => true, 'message' => ''];
            }

            // For salary furlough subtypes, use static_type
            $chronoType = $countData['static_type'];
            $count = $countData['count'];

            \Log::info('TimeRequestDateConstraintService::validateCountLimitForConstraints - Processing', [
                'chronoType' => $chronoType,
                'count' => $count
            ]);

            // Get current date for period calculation
            $now = Carbon::now();

            // Get the period start and end dates based on chrono type
            $periodDates = $this->getPeriodDatesForConstraints($now, $chronoType);

            if (!$periodDates) {
                \Log::warning('TimeRequestDateConstraintService::validateCountLimitForConstraints - Could not calculate period dates');
                return ['valid' => true, 'message' => ''];
            }

            \Log::info('TimeRequestDateConstraintService::validateCountLimitForConstraints - Period calculated', [
                'start' => $periodDates['start']->format('Y-m-d'),
                'end' => $periodDates['end']->format('Y-m-d')
            ]);

            // Get current user's employee ID
            $cn = ConnectionService::getCNForEmployeeUser();
            $user = auth()->user();

            if (!$user) {
                \Log::warning('TimeRequestDateConstraintService::validateCountLimitForConstraints - No authenticated user');
                return ['valid' => true, 'message' => ''];
            }

            $employee = Employee::on($cn)->where(Employee::PHONE, $user->phone)->first();

            if (!$employee) {
                \Log::warning('TimeRequestDateConstraintService::validateCountLimitForConstraints - No employee found');
                return ['valid' => true, 'message' => ''];
            }

            // Get attendance status from status ID
            $employeeAttendanceDtl = app(EmployeeAttendanceDtl::class);
            $attStatus = $this->getAttStatusFromId($attStatusId);

            if (!$attStatus) {
                \Log::warning('TimeRequestDateConstraintService::validateCountLimitForConstraints - Could not determine att_status');
                return ['valid' => true, 'message' => ''];
            }

            // Count existing TimeRequest records with same att_status within the period
            $existingRequestsCount = TimeRequest::on($cn)
                ->where(TimeRequest::EMPLOYEE_ID, $employee->id)
                ->where(TimeRequest::ATT_STATUS, $attStatus)
                ->where(TimeRequest::BEGIN_DATE, '>=', $periodDates['start'])
                ->where(TimeRequest::BEGIN_DATE, '<=', $periodDates['end'])
                ->whereIn(TimeRequest::CONFIRM_STATUS, [
                    TimeRequest::CONFIRM_STATUS_SENT,
                    TimeRequest::CONFIRM_STATUS_CONFIRMED,
                    TimeRequest::CONFIRM_STATUS_TEMP_CONFIRMED
                ]) // Only count non-cancelled requests
                ->count();

            \Log::info('TimeRequestDateConstraintService::validateCountLimitForConstraints - Existing requests counted', [
                'existingRequestsCount' => $existingRequestsCount,
                'countLimit' => $count
            ]);

            // Check if adding this request would exceed the limit
            if ($existingRequestsCount >= $count) {
                $attStatusName = $this->getAttStatusNameFromId($attStatusId);
                $sessionTypeName = $this->getSessionTypeNameForConstraints($chronoType);
                $periodStart = $periodDates['start']->format('Y-m-d');
                $periodEnd = $periodDates['end']->format('Y-m-d');

                $message = "{$attStatusName}-ийн хүсэлтийн тоо одоогийн {$sessionTypeName} хугацаанд ({$periodStart} - {$periodEnd}) хязгаарлагдсан тооноос ({$count}) хэтэрсэн байна. Одоогийн тоо: {$existingRequestsCount}";

                \Log::info('TimeRequestDateConstraintService::validateCountLimitForConstraints - Count limit exceeded', [
                    'message' => $message
                ]);

                return [
                    'valid' => false,
                    'message' => $message
                ];
            }

            \Log::info('TimeRequestDateConstraintService::validateCountLimitForConstraints - Count limit OK');
            return ['valid' => true, 'message' => ''];

        } catch (\Exception $e) {
            \Log::error('TimeRequestDateConstraintService::validateCountLimitForConstraints - Error', [
                'error' => $e->getMessage(),
                'countFormula' => $countFormula,
                'attStatusId' => $attStatusId
            ]);
            return ['valid' => true, 'message' => ''];
        }
    }

    /**
     * Get period start and end dates for constraints based on chrono type
     * Similar to TimeRequestValidationService::getPeriodDates
     */
    private function getPeriodDatesForConstraints($date, $chronoType)
    {
        $month = $date->month;
        $year = $date->year;

        switch ($chronoType) {
            case 'month':
                return [
                    'start' => Carbon::create($year, $month, 1),
                    'end' => Carbon::create($year, $month)->endOfMonth()
                ];

            case 'quarter':
                if ($month <= 3) {
                    return [
                        'start' => Carbon::create($year, 1, 1),
                        'end' => Carbon::create($year, 3, 31)
                    ];
                } elseif ($month <= 6) {
                    return [
                        'start' => Carbon::create($year, 4, 1),
                        'end' => Carbon::create($year, 6, 30)
                    ];
                } elseif ($month <= 9) {
                    return [
                        'start' => Carbon::create($year, 7, 1),
                        'end' => Carbon::create($year, 9, 30)
                    ];
                } else {
                    return [
                        'start' => Carbon::create($year, 10, 1),
                        'end' => Carbon::create($year, 12, 31)
                    ];
                }

            case 'halfyear':
                if ($month <= 6) {
                    return [
                        'start' => Carbon::create($year, 1, 1),
                        'end' => Carbon::create($year, 6, 30)
                    ];
                } else {
                    return [
                        'start' => Carbon::create($year, 7, 1),
                        'end' => Carbon::create($year, 12, 31)
                    ];
                }

            case 'year':
                return [
                    'start' => Carbon::create($year, 1, 1),
                    'end' => Carbon::create($year, 12, 31)
                ];

            default:
                return null;
        }
    }

    /**
     * Get attendance status numeric value from status ID string
     */
    private function getAttStatusFromId($attStatusId)
    {
        // Map status IDs to their numeric values
        $statusMap = [
            'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY' => EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY,
            'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_KID_BIRTHDAY' => EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_KID_BIRTHDAY,
            'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_HALFDAY' => EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_HALFDAY,
            'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE' => EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE,
        ];

        return $statusMap[$attStatusId] ?? null;
    }

    /**
     * Get attendance status name from status ID string
     */
    private function getAttStatusNameFromId($attStatusId)
    {
        // Map status IDs to their names
        $nameMap = [
            'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY' => EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH . ' (' . EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_BIRTHDAY . ')',
            'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_KID_BIRTHDAY' => EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH . ' (' . EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_KID_BIRTHDAY . ')',
            'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_HALFDAY' => EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH . ' (' . EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_HALFDAY . ')',
            'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE' => EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH . ' (' . EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_NOSMOKE . ')',
        ];

        return $nameMap[$attStatusId] ?? 'Unknown Status';
    }

    /**
     * Get session type name for constraints
     * Uses same logic as TimeRequestValidationService::getSessionTypeName
     */
    private function getSessionTypeNameForConstraints($chronoType)
    {
        switch ($chronoType) {
            case 'month':
                return 'сарын';
            case 'quarter':
                return 'улирлын';
            case 'halfyear':
                return 'хагас жилийн';
            case 'year':
                return 'жилийн';
            default:
                return 'хугацааны';
        }
    }

    /**
     * Calculate session-based minimum date
     * If countLimitValid is true: use previous session end date + 1 day
     * If countLimitValid is false: use current session end date + 1 day
     * Follows same logic as TimeRequestValidationService
     */
    private function calculateSessionMinDate($countFormula, $attStatusId, $countLimitValid)
    {
        \Log::info('TimeRequestDateConstraintService::calculateSessionMinDate - Start', [
            'countFormula' => $countFormula,
            'attStatusId' => $attStatusId,
            'countLimitValid' => $countLimitValid
        ]);

        try {
            $countData = ChronoCountComponent::parseFormula($countFormula);

            if (!$countData['static_type']) {
                \Log::warning('TimeRequestDateConstraintService::calculateSessionMinDate - No static_type found');
                return null;
            }

            // For salary furlough subtypes, use static_type
            $chronoType = $countData['static_type'];
            $now = Carbon::now();

            \Log::info('TimeRequestDateConstraintService::calculateSessionMinDate - Processing', [
                'chronoType' => $chronoType,
                'now' => $now->format('Y-m-d H:i:s')
            ]);

            // Use the same logic as TimeRequestValidationService
            $validationService = app(TimeRequestValidationService::class);
            $reflection = new \ReflectionClass($validationService);
            $method = $reflection->getMethod('getCurrentSessionEndDate');
            $method->setAccessible(true);

            if ($countLimitValid) {
                // Count limit is valid, use previous session end date
                $previousSessionDate = $this->getPreviousSessionDate($now, $chronoType);
                $sessionEndDate = $method->invoke($validationService, $previousSessionDate, $chronoType);

                \Log::info('TimeRequestDateConstraintService::calculateSessionMinDate - Using previous session', [
                    'previousSessionDate' => $previousSessionDate->format('Y-m-d'),
                    'sessionEndDate' => $sessionEndDate ? $sessionEndDate->format('Y-m-d') : null
                ]);
            } else {
                // Count limit exceeded, use current session end date
                $sessionEndDate = $method->invoke($validationService, $now, $chronoType);

                \Log::info('TimeRequestDateConstraintService::calculateSessionMinDate - Using current session', [
                    'sessionEndDate' => $sessionEndDate ? $sessionEndDate->format('Y-m-d') : null
                ]);
            }

            if (!$sessionEndDate) {
                \Log::warning('TimeRequestDateConstraintService::calculateSessionMinDate - No session end date calculated');
                return null;
            }

            // Return the day after the session ends
            $result = $sessionEndDate->addDay();

            \Log::info('TimeRequestDateConstraintService::calculateSessionMinDate - End', [
                'result' => $result->format('Y-m-d H:i:s')
            ]);

            return $result;

        } catch (\Exception $e) {
            \Log::error('TimeRequestDateConstraintService::calculateSessionMinDate - Error', [
                'error' => $e->getMessage(),
                'countFormula' => $countFormula,
                'attStatusId' => $attStatusId
            ]);
            return null;
        }
    }

    /**
     * Get the date for the previous session based on chrono type
     */
    private function getPreviousSessionDate($currentDate, $chronoType)
    {
        switch ($chronoType) {
            case 'month':
                return $currentDate->copy()->subMonth();
            case 'quarter':
                return $currentDate->copy()->subMonths(3);
            case 'halfyear':
                return $currentDate->copy()->subMonths(6);
            case 'year':
                return $currentDate->copy()->subYear();
            default:
                return $currentDate->copy()->subMonth(); // Default to previous month
        }
    }

    /**
     * Determine if minute precision should be used based on chrono types
     * If any chrono type is less than 'day', use minute precision
     *
     * @param array $chronoTypes
     * @return bool
     */
    private function shouldUseMinutePrecision($chronoTypes)
    {
        $subDayTypes = ['minute', 'hour', 'halfday'];

        foreach ($chronoTypes as $type) {
            if (in_array($type, $subDayTypes)) {
                return true;
            }
        }

        return false;
    }
}
