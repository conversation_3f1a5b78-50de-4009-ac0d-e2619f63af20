<?php

namespace Tests\Unit\Services\Time;

use Tests\TestCase;
use App\Services\Time\TimeRequestValidationService;
use App\Models\Customer\Configuration;
use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use App\Models\Customer\Time\TimeRequest\TimeRequest;
use App\Models\Customer\Employee;
use App\Services\ConnectionService;
use Carbon\Carbon;
use Mockery;

class TimeRequestValidationServiceTest extends TestCase
{

    protected $validationService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->validationService = new TimeRequestValidationService();
    }

    /** @test */
    public function it_allows_request_when_no_configuration_exists()
    {
        // Mock ConnectionService
        $this->mock(ConnectionService::class, function ($mock) {
            $mock->shouldReceive('getCNForEmployeeUser')->andReturn('test_connection');
        });

        // Mock Configuration model to return null
        $this->mock(Configuration::class, function ($mock) {
            $mock->shouldReceive('on')->andReturnSelf();
            $mock->shouldReceive('where')->andReturnSelf();
            $mock->shouldReceive('first')->andReturn(null);
        });

        $result = $this->validationService->validateValue1(
            '2024-01-15',
            EmployeeAttendanceDtl::ATTENDANCE_STATUS_WORK
        );

        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);
    }

    /** @test */
    public function it_validates_begin_date_against_anchor_configuration()
    {
        // Set current time for testing
        Carbon::setTestNow('2024-01-10 10:00:00');

        // Mock ConnectionService
        $this->mock(ConnectionService::class, function ($mock) {
            $mock->shouldReceive('getCNForEmployeeUser')->andReturn('test_connection');
        });

        // Create a mock configuration with anchor formula: "anchor,now,day,3"
        // This means begin_date should be >= now + 3 days = 2024-01-13
        $mockConfig = new \stdClass();
        $mockConfig->config_id = 'TIME_REQUEST_ATTENDANCE_STATUS_WORK_MIN_DATE';
        $mockConfig->value1 = 'anchor,now,day,3';

        $this->mock(Configuration::class, function ($mock) use ($mockConfig) {
            $mock->shouldReceive('on')->andReturnSelf();
            $mock->shouldReceive('where')->andReturnSelf();
            $mock->shouldReceive('first')->andReturn($mockConfig);
        });

        // Test with date before minimum (should fail)
        $result = $this->validationService->validateValue1(
            '2024-01-12', // Before 2024-01-13
            EmployeeAttendanceDtl::ATTENDANCE_STATUS_WORK
        );

        $this->assertFalse($result['valid']);
        $this->assertStringContains('2024-01-13', $result['message']);

        // Test with date after minimum (should pass)
        $result = $this->validationService->validateValue1(
            '2024-01-15', // After 2024-01-13
            EmployeeAttendanceDtl::ATTENDANCE_STATUS_WORK
        );

        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);
    }

    /** @test */
    public function it_handles_custom_date_anchor_configuration()
    {
        // Mock ConnectionService
        $this->mock(ConnectionService::class, function ($mock) {
            $mock->shouldReceive('getCNForEmployeeUser')->andReturn('test_connection');
        });

        // Create a mock configuration with custom date anchor: "anchor,2024-01-01,day,5"
        // This means begin_date should be >= 2024-01-01 + 5 days = 2024-01-06
        $mockConfig = new \stdClass();
        $mockConfig->config_id = 'TIME_REQUEST_ATTENDANCE_STATUS_FURLOUGH_MIN_DATE';
        $mockConfig->value1 = 'anchor,2024-01-01,day,5';

        $this->mock(Configuration::class, function ($mock) use ($mockConfig) {
            $mock->shouldReceive('on')->andReturnSelf();
            $mock->shouldReceive('where')->andReturnSelf();
            $mock->shouldReceive('first')->andReturn($mockConfig);
        });

        // Test with date before minimum (should fail)
        $result = $this->validationService->validateValue1(
            '2024-01-05', // Before 2024-01-06
            EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH
        );

        $this->assertFalse($result['valid']);
        $this->assertStringContains('2024-01-06', $result['message']);

        // Test with date after minimum (should pass)
        $result = $this->validationService->validateValue1(
            '2024-01-10', // After 2024-01-06
            EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH
        );

        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);
    }

    /** @test */
    public function it_handles_different_time_units()
    {
        Carbon::setTestNow('2024-01-10 10:00:00');

        $this->mock(ConnectionService::class, function ($mock) {
            $mock->shouldReceive('getCNForEmployeeUser')->andReturn('test_connection');
        });

        // Test with month unit: "anchor,now,month,1"
        $mockConfig = new \stdClass();
        $mockConfig->config_id = 'TIME_REQUEST_ATTENDANCE_STATUS_SICK_MIN_DATE';
        $mockConfig->value1 = 'anchor,now,month,1';

        $this->mock(Configuration::class, function ($mock) use ($mockConfig) {
            $mock->shouldReceive('on')->andReturnSelf();
            $mock->shouldReceive('where')->andReturnSelf();
            $mock->shouldReceive('first')->andReturn($mockConfig);
        });

        // Test with date before minimum (should fail)
        $result = $this->validationService->validateValue1(
            '2024-01-15', // Before 2024-02-10
            EmployeeAttendanceDtl::ATTENDANCE_STATUS_SICK
        );

        $this->assertFalse($result['valid']);
        $this->assertStringContains('Өвчтэй', $result['message']);
    }

    /** @test */
    public function it_handles_invalid_formula_gracefully()
    {
        $this->mock(ConnectionService::class, function ($mock) {
            $mock->shouldReceive('getCNForEmployeeUser')->andReturn('test_connection');
        });

        // Create a mock configuration with invalid formula
        $mockConfig = new \stdClass();
        $mockConfig->config_id = 'TIME_REQUEST_ATTENDANCE_STATUS_WORK_MIN_DATE';
        $mockConfig->value1 = 'invalid-formula';

        $this->mock(Configuration::class, function ($mock) use ($mockConfig) {
            $mock->shouldReceive('on')->andReturnSelf();
            $mock->shouldReceive('where')->andReturnSelf();
            $mock->shouldReceive('first')->andReturn($mockConfig);
        });

        $result = $this->validationService->validateValue1(
            '2024-01-15',
            EmployeeAttendanceDtl::ATTENDANCE_STATUS_WORK
        );

        // Should allow the request when formula is invalid
        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);
    }

    /** @test */
    public function it_allows_request_when_not_salary_furlough_config()
    {
        // Mock ConnectionService
        $this->mock(ConnectionService::class, function ($mock) {
            $mock->shouldReceive('getCNForEmployeeUser')->andReturn('test_connection');
        });

        $result = $this->validationService->validateValue(
            '2024-01-15',
            EmployeeAttendanceDtl::ATTENDANCE_STATUS_WORK // Not a salary furlough status
        );

        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);
    }

    /** @test */
    public function it_validates_salary_furlough_quarter_logic()
    {
        // Mock ConnectionService
        $this->mock(ConnectionService::class, function ($mock) {
            $mock->shouldReceive('getCNForEmployeeUser')->andReturn('test_connection');
        });

        // Mock EmployeeAttendanceDtl
        $this->mock(EmployeeAttendanceDtl::class, function ($mock) {
            $mock->shouldReceive('getAttStatusId')
                ->with(EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY)
                ->andReturn('ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY');
            $mock->shouldReceive('getAttStatusName')->andReturn('Төрсөн өдрийн чөлөө');
        });

        // Create a mock configuration with count formula: "count,day,quarter,1,3"
        $mockConfig = new \stdClass();
        $mockConfig->config_id = 'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY';
        $mockConfig->value = 'count,day,quarter,1,3';

        $this->mock(Configuration::class, function ($mock) use ($mockConfig) {
            $mock->shouldReceive('on')->andReturnSelf();
            $mock->shouldReceive('where')
                ->with(Configuration::CONFIG_ID, 'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY')
                ->andReturnSelf();
            $mock->shouldReceive('first')->andReturn($mockConfig);
        });

        // Test with date in current quarter (should fail)
        $result = $this->validationService->validateValue(
            '2024-02-15', // In Q1 (Jan-Mar)
            EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY
        );

        $this->assertFalse($result['valid']);
        $this->assertStringContains('улирлын дуусах огноо', $result['message']);

        // Test with date after current quarter (should pass)
        $result = $this->validationService->validateValue(
            '2024-04-15', // In Q2 (Apr-Jun), after Q1 ends
            EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY
        );

        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);
    }

    /** @test */
    public function it_allows_request_when_no_value_configuration_exists()
    {
        // Mock ConnectionService
        $this->mock(ConnectionService::class, function ($mock) {
            $mock->shouldReceive('getCNForEmployeeUser')->andReturn('test_connection');
        });

        // Mock Configuration model to return null
        $this->mock(Configuration::class, function ($mock) {
            $mock->shouldReceive('on')->andReturnSelf();
            $mock->shouldReceive('where')->andReturnSelf();
            $mock->shouldReceive('first')->andReturn(null);
        });

        $result = $this->validationService->validateValue(
            '2024-01-15',
            EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY
        );

        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);
    }

    /** @test */
    public function it_calculates_session_end_dates_correctly()
    {
        $service = new TimeRequestValidationService();
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('getCurrentSessionEndDate');
        $method->setAccessible(true);

        // Test month
        $date = Carbon::create(2024, 2, 15);
        $result = $method->invoke($service, $date, 'month');
        $this->assertEquals('2024-02-29', $result->format('Y-m-d')); // February 2024 has 29 days

        // Test quarter
        $date = Carbon::create(2024, 2, 15);
        $result = $method->invoke($service, $date, 'quarter');
        $this->assertEquals('2024-03-31', $result->format('Y-m-d')); // Q1 ends March 31

        $date = Carbon::create(2024, 5, 15);
        $result = $method->invoke($service, $date, 'quarter');
        $this->assertEquals('2024-06-30', $result->format('Y-m-d')); // Q2 ends June 30

        // Test halfyear
        $date = Carbon::create(2024, 3, 15);
        $result = $method->invoke($service, $date, 'halfyear');
        $this->assertEquals('2024-06-30', $result->format('Y-m-d')); // First half ends June 30

        $date = Carbon::create(2024, 8, 15);
        $result = $method->invoke($service, $date, 'halfyear');
        $this->assertEquals('2024-12-31', $result->format('Y-m-d')); // Second half ends December 31

        // Test year
        $date = Carbon::create(2024, 6, 15);
        $result = $method->invoke($service, $date, 'year');
        $this->assertEquals('2024-12-31', $result->format('Y-m-d')); // Year ends December 31
    }

    /** @test */
    public function it_gets_correct_session_type_names()
    {
        $service = new TimeRequestValidationService();
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('getSessionTypeName');
        $method->setAccessible(true);

        $this->assertEquals('сарын', $method->invoke($service, 'month'));
        $this->assertEquals('улирлын', $method->invoke($service, 'quarter'));
        $this->assertEquals('хагас жилийн', $method->invoke($service, 'halfyear'));
        $this->assertEquals('жилийн', $method->invoke($service, 'year'));
        $this->assertEquals('хугацааны', $method->invoke($service, 'unknown'));
    }

    /** @test */
    public function it_gets_correct_period_dates()
    {
        $service = new TimeRequestValidationService();
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('getPeriodDates');
        $method->setAccessible(true);

        // Test month
        $date = Carbon::create(2024, 2, 15);
        $result = $method->invoke($service, $date, 'month');
        $this->assertEquals('2024-02-01', $result['start']->format('Y-m-d'));
        $this->assertEquals('2024-02-29', $result['end']->format('Y-m-d'));

        // Test quarter
        $date = Carbon::create(2024, 2, 15);
        $result = $method->invoke($service, $date, 'quarter');
        $this->assertEquals('2024-01-01', $result['start']->format('Y-m-d'));
        $this->assertEquals('2024-03-31', $result['end']->format('Y-m-d'));

        // Test halfyear
        $date = Carbon::create(2024, 3, 15);
        $result = $method->invoke($service, $date, 'halfyear');
        $this->assertEquals('2024-01-01', $result['start']->format('Y-m-d'));
        $this->assertEquals('2024-06-30', $result['end']->format('Y-m-d'));

        // Test year
        $date = Carbon::create(2024, 6, 15);
        $result = $method->invoke($service, $date, 'year');
        $this->assertEquals('2024-01-01', $result['start']->format('Y-m-d'));
        $this->assertEquals('2024-12-31', $result['end']->format('Y-m-d'));
    }

    /** @test */
    public function it_validates_count_limit_logic_structure()
    {
        // This test verifies the count limit validation logic structure
        // without complex database mocking

        $service = new TimeRequestValidationService();
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('validateCountLimit');
        $method->setAccessible(true);

        // Test with no authenticated user (should allow)
        $beginDate = Carbon::create(2024, 2, 15);
        $result = $method->invoke($service, $beginDate, 3, 'month', EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY);

        // Should return valid=true when no user is authenticated
        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);
    }

    /** @test */
    public function it_validates_complete_salary_furlough_flow()
    {
        // This test demonstrates the complete validation flow for salary furlough requests
        // It shows how both validateValue1 and validateValue work together

        $service = new TimeRequestValidationService();

        // Test with a non-salary furlough status (should use validateValue1 only)
        $result1 = $service->validateValue1('2024-02-15', EmployeeAttendanceDtl::ATTENDANCE_STATUS_WORK);
        $this->assertTrue($result1['valid']); // Should pass since no configuration exists

        $result2 = $service->validateValue('2024-02-15', EmployeeAttendanceDtl::ATTENDANCE_STATUS_WORK);
        $this->assertTrue($result2['valid']); // Should pass since it's not a salary furlough status

        // Test with a salary furlough status (should use both validations)
        $result3 = $service->validateValue1('2024-02-15', EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY);
        $this->assertTrue($result3['valid']); // Should pass since no configuration exists

        $result4 = $service->validateValue('2024-02-15', EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY);
        $this->assertTrue($result4['valid']); // Should pass since no configuration exists or no user authenticated
    }

    /** @test */
    public function it_validates_vacation_request_structure()
    {
        // Test the vacation validation method structure
        $service = new TimeRequestValidationService();
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('validateVacationRequest');
        $method->setAccessible(true);

        // Test with invalid formula (should allow)
        $result = $method->invoke($service, '2024-02-15', 'invalid-formula', EmployeeAttendanceDtl::ATTENDANCE_STATUS_VACATION);
        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);

        // Test with valid anchor formula but no user authenticated (should allow)
        // Use a future date that would be valid even with a 30-day shift
        $futureDate = Carbon::now()->addDays(60)->format('Y-m-d');
        $result = $method->invoke($service, $futureDate, 'anchor,now,day,30', EmployeeAttendanceDtl::ATTENDANCE_STATUS_VACATION);
        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);
    }

    /** @test */
    public function it_applies_shift_to_date_correctly()
    {
        $service = new TimeRequestValidationService();
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('applyShiftToDate');
        $method->setAccessible(true);

        $baseDate = Carbon::create(2024, 1, 15);

        // Test different shift types
        $result = $method->invoke($service, $baseDate, 'day', 30);
        $this->assertEquals('2024-02-14', $result->format('Y-m-d'));

        $result = $method->invoke($service, $baseDate, 'month', 6);
        $this->assertEquals('2024-07-15', $result->format('Y-m-d'));

        $result = $method->invoke($service, $baseDate, 'year', 1);
        $this->assertEquals('2025-01-15', $result->format('Y-m-d'));

        // Test invalid type
        $result = $method->invoke($service, $baseDate, 'invalid', 1);
        $this->assertNull($result);
    }

    /** @test */
    public function it_handles_vacation_validation_for_different_attendance_statuses()
    {
        $service = new TimeRequestValidationService();

        // Test vacation status (should be handled by vacation logic)
        $result = $service->validateValue('2024-02-15', EmployeeAttendanceDtl::ATTENDANCE_STATUS_VACATION);
        $this->assertTrue($result['valid']); // Should pass since no configuration exists

        // Test non-vacation, non-salary-furlough status (should be skipped)
        $result = $service->validateValue('2024-02-15', EmployeeAttendanceDtl::ATTENDANCE_STATUS_WORK);
        $this->assertTrue($result['valid']); // Should pass since it's not handled by this validation
    }

    /** @test */
    public function it_validates_furlough_duration_correctly()
    {
        $service = new TimeRequestValidationService();
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('validateFurloughDuration');
        $method->setAccessible(true);

        // Test with no end date (should allow)
        $result = $method->invoke($service, '2024-02-15', null, 'range,day,0,2', EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH_SHORT);
        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);

        // Test with invalid formula (should allow)
        $result = $method->invoke($service, '2024-02-15', '2024-02-17', 'invalid-formula', EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH_SHORT);
        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);

        // Test with valid duration within range (should pass)
        $result = $method->invoke($service, '2024-02-15', '2024-02-16', 'range,day,0,2', EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH_SHORT);
        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);

        // Test with duration outside range (should fail)
        $result = $method->invoke($service, '2024-02-15', '2024-02-20', 'range,day,0,2', EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH_SHORT);
        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('0-2 өдөр байх ёстой', $result['message']);
        $this->assertStringContainsString('Одоогийн зөрүү: 5 өдөр', $result['message']);
    }

    /** @test */
    public function it_calculates_duration_difference_correctly()
    {
        $service = new TimeRequestValidationService();
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('calculateDurationDifference');
        $method->setAccessible(true);

        $beginDate = Carbon::create(2024, 2, 15, 10, 0, 0);
        $endDate = Carbon::create(2024, 2, 17, 14, 30, 0);

        // Test day difference
        $result = $method->invoke($service, $beginDate, $endDate, 'day');
        $this->assertEquals(2, $result);

        // Test hour difference
        $result = $method->invoke($service, $beginDate, $endDate, 'hour');
        $this->assertEquals(52, $result); // 2 days + 4.5 hours = 52 hours

        // Test invalid type
        $result = $method->invoke($service, $beginDate, $endDate, 'invalid');
        $this->assertNull($result);
    }

    /** @test */
    public function it_gets_correct_chrono_type_names()
    {
        $service = new TimeRequestValidationService();
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('getChronoTypeName');
        $method->setAccessible(true);

        $this->assertEquals('өдөр', $method->invoke($service, 'day'));
        $this->assertEquals('цаг', $method->invoke($service, 'hour'));
        $this->assertEquals('сар', $method->invoke($service, 'month'));
        $this->assertEquals('жил', $method->invoke($service, 'year'));
        $this->assertEquals('хугацаа', $method->invoke($service, 'unknown'));
    }

    /** @test */
    public function it_validates_and_logic_structure_correctly()
    {
        // This test verifies the AND logic structure without complex database mocking
        $service = new TimeRequestValidationService();
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('validateAgainstExistingVacationRequests');
        $method->setAccessible(true);

        // Test with no existing requests (should pass)
        $this->mock(TimeRequest::class, function ($mock) {
            $mock->shouldReceive('on')->andReturnSelf();
            $mock->shouldReceive('where')->andReturnSelf();
            $mock->shouldReceive('whereIn')->andReturnSelf();
            $mock->shouldReceive('get')->andReturn(collect([])); // No existing requests
        });

        $newBeginDate = Carbon::create(2024, 2, 10);
        $result = $method->invoke($service, $newBeginDate, 123, 11, 'day', 30, 'test_cn');

        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);
    }

    /** @test */
    public function it_validates_vacation_with_both_conditions_and_logic()
    {
        // This test demonstrates that BOTH conditions must be satisfied (AND logic)
        $service = new TimeRequestValidationService();
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('validateVacationWithCompanyWorkDate');
        $method->setAccessible(true);

        // Test with no authenticated user (should allow due to graceful fallback)
        $beginDate = Carbon::create(2024, 6, 15);
        $anchorData = [
            'type' => 'day',
            'shift_count' => 365
        ];

        $result = $method->invoke($service, $beginDate, $anchorData, EmployeeAttendanceDtl::ATTENDANCE_STATUS_VACATION);

        // Should return valid=true when no user is authenticated (graceful fallback)
        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);
    }

    protected function tearDown(): void
    {
        Carbon::setTestNow();
        parent::tearDown();
    }
}
