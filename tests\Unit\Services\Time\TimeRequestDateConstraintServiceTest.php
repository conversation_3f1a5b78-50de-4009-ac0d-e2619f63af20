<?php

namespace Tests\Unit\Services\Time;

use Tests\TestCase;
use App\Services\Time\TimeRequestDateConstraintService;
use App\Models\Customer\Configuration;
use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use App\Models\Customer\Employee;
use App\Services\ConnectionService;
use Carbon\Carbon;
use Mockery;

class TimeRequestDateConstraintServiceTest extends TestCase
{
    protected $constraintService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->constraintService = new TimeRequestDateConstraintService();
    }

    /** @test */
    public function it_returns_none_for_unknown_attendance_status()
    {
        // Mock EmployeeAttendanceDtl to return null for unknown status
        $this->mock(EmployeeAttendanceDtl::class, function ($mock) {
            $mock->shouldReceive('getAttStatusId')->andReturn(null);
        });

        $result = $this->constraintService->getDateConstraints(999);

        $this->assertEquals('none', $result['type']);
        $this->assertNull($result['min_date']);
        $this->assertNull($result['max_date']);
        $this->assertEquals('', $result['message']);
    }

    /** @test */
    public function it_uses_eloquent_for_configuration_access()
    {
        // Test that the service uses Eloquent properly
        // Mock EmployeeAttendanceDtl
        $this->mock(EmployeeAttendanceDtl::class, function ($mock) {
            $mock->shouldReceive('getAttStatusId')
                ->with(EmployeeAttendanceDtl::ATTENDANCE_STATUS_VACATION)
                ->andReturn('ATTENDANCE_STATUS_VACATION');
        });

        // Mock ConnectionService
        $this->mock(ConnectionService::class, function ($mock) {
            $mock->shouldReceive('getCNForEmployeeUser')->andReturn('test_connection');
        });

        // Mock Configuration model
        $this->mock(Configuration::class, function ($mock) {
            $mock->shouldReceive('on')->andReturnSelf();
            $mock->shouldReceive('where')->andReturnSelf();
            $mock->shouldReceive('first')->andReturn(null);
        });

        $result = $this->constraintService->getDateConstraints(EmployeeAttendanceDtl::ATTENDANCE_STATUS_VACATION);

        // Should return none when no configuration exists
        $this->assertEquals('none', $result['type']);
    }

    /** @test */
    public function it_handles_out_work_status_with_value1_only()
    {
        // Mock EmployeeAttendanceDtl
        $this->mock(EmployeeAttendanceDtl::class, function ($mock) {
            $mock->shouldReceive('getAttStatusId')
                ->with(EmployeeAttendanceDtl::ATTENDANCE_STATUS_OUT_WORK)
                ->andReturn('ATTENDANCE_STATUS_OUT_WORK');
        });

        // Mock ConnectionService
        $this->mock(ConnectionService::class, function ($mock) {
            $mock->shouldReceive('getCNForEmployeeUser')->andReturn('test_connection');
        });

        // Mock Configuration for value1
        $mockConfig = new \stdClass();
        $mockConfig->config_id = 'ATTENDANCE_STATUS_OUT_WORK';
        $mockConfig->value1 = 'anchor,now,day,3';

        $this->mock(Configuration::class, function ($mock) use ($mockConfig) {
            $mock->shouldReceive('on')->andReturnSelf();
            $mock->shouldReceive('where')
                ->with(Configuration::CONFIG_ID, 'ATTENDANCE_STATUS_OUT_WORK')
                ->andReturnSelf();
            $mock->shouldReceive('first')->andReturn($mockConfig);
        });

        $result = $this->constraintService->getDateConstraints(EmployeeAttendanceDtl::ATTENDANCE_STATUS_OUT_WORK);

        $this->assertEquals('min_date', $result['type']);
        $this->assertNotNull($result['min_date']);
        $this->assertNull($result['max_date']);
        $this->assertStringContainsString('Requests must be after', $result['message']);
    }

    /** @test */
    public function it_handles_furlough_long_status_with_value1_only()
    {
        // Mock EmployeeAttendanceDtl
        $this->mock(EmployeeAttendanceDtl::class, function ($mock) {
            $mock->shouldReceive('getAttStatusId')
                ->with(EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH_LONG)
                ->andReturn('ATTENDANCE_STATUS_FURLOUGH_LONG');
        });

        // Mock ConnectionService
        $this->mock(ConnectionService::class, function ($mock) {
            $mock->shouldReceive('getCNForEmployeeUser')->andReturn('test_connection');
        });

        // Mock Configuration for value1
        $mockConfig = new \stdClass();
        $mockConfig->config_id = 'ATTENDANCE_STATUS_FURLOUGH.ATTENDANCE_STATUS_FURLOUGH_LONG';
        $mockConfig->value1 = 'anchor,now,day,7';

        $this->mock(Configuration::class, function ($mock) use ($mockConfig) {
            $mock->shouldReceive('on')->andReturnSelf();
            $mock->shouldReceive('where')
                ->with(Configuration::CONFIG_ID, 'ATTENDANCE_STATUS_FURLOUGH.ATTENDANCE_STATUS_FURLOUGH_LONG')
                ->andReturnSelf();
            $mock->shouldReceive('first')->andReturn($mockConfig);
        });

        $result = $this->constraintService->getDateConstraints(EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH_LONG);

        $this->assertEquals('min_date', $result['type']);
        $this->assertNotNull($result['min_date']);
        $this->assertNull($result['max_date']);
        $this->assertStringContainsString('Requests must be after', $result['message']);
    }

    /** @test */
    public function it_handles_vacation_status_with_max_of_value_and_value1()
    {
        // Mock EmployeeAttendanceDtl
        $this->mock(EmployeeAttendanceDtl::class, function ($mock) {
            $mock->shouldReceive('getAttStatusId')
                ->with(EmployeeAttendanceDtl::ATTENDANCE_STATUS_VACATION)
                ->andReturn('ATTENDANCE_STATUS_VACATION');
        });

        // Mock ConnectionService
        $this->mock(ConnectionService::class, function ($mock) {
            $mock->shouldReceive('getCNForEmployeeUser')->andReturn('test_connection');
        });

        // Mock Configuration for both value and value1 in single record
        $config = new \stdClass();
        $config->config_id = 'ATTENDANCE_STATUS_VACATION';
        $config->value = 'anchor,employee_dtl.company_work_date,day,365';
        $config->value1 = 'anchor,now,day,30';

        $this->mock(Configuration::class, function ($mock) use ($config) {
            $mock->shouldReceive('on')->andReturnSelf();
            $mock->shouldReceive('where')
                ->with(Configuration::CONFIG_ID, 'ATTENDANCE_STATUS_VACATION')
                ->andReturnSelf();
            $mock->shouldReceive('first')->andReturn($config);
        });

        $result = $this->constraintService->getDateConstraints(EmployeeAttendanceDtl::ATTENDANCE_STATUS_VACATION);

        $this->assertEquals('min_date', $result['type']);
        $this->assertNotNull($result['min_date']);
        $this->assertNull($result['max_date']);
        $this->assertStringContainsString('Vacation requests must be after', $result['message']);
    }

    /** @test */
    public function it_handles_salary_furlough_status_with_max_of_value_and_value1()
    {
        // Mock EmployeeAttendanceDtl
        $this->mock(EmployeeAttendanceDtl::class, function ($mock) {
            $mock->shouldReceive('getAttStatusId')
                ->with(EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY)
                ->andReturn('ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY');
        });

        // Mock ConnectionService
        $this->mock(ConnectionService::class, function ($mock) {
            $mock->shouldReceive('getCNForEmployeeUser')->andReturn('test_connection');
        });

        // Mock Configuration for both value and value1 in single record
        $config = new \stdClass();
        $config->config_id = 'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY';
        $config->value = 'count,day,quarter,1,3';
        $config->value1 = 'anchor,now,day,15';

        $this->mock(Configuration::class, function ($mock) use ($config) {
            $mock->shouldReceive('on')->andReturnSelf();
            $mock->shouldReceive('where')
                ->with(Configuration::CONFIG_ID, 'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY')
                ->andReturnSelf();
            $mock->shouldReceive('first')->andReturn($config);
        });

        $result = $this->constraintService->getDateConstraints(EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY);

        $this->assertEquals('min_date', $result['type']);
        $this->assertNotNull($result['min_date']);
        $this->assertNull($result['max_date']);
        $this->assertStringContainsString('Salary furlough requests must be after', $result['message']);
    }

    /** @test */
    public function it_returns_none_when_no_configurations_exist()
    {
        // Mock EmployeeAttendanceDtl
        $this->mock(EmployeeAttendanceDtl::class, function ($mock) {
            $mock->shouldReceive('getAttStatusId')
                ->with(EmployeeAttendanceDtl::ATTENDANCE_STATUS_OUT_WORK)
                ->andReturn('ATTENDANCE_STATUS_OUT_WORK');
        });

        // Mock ConnectionService
        $this->mock(ConnectionService::class, function ($mock) {
            $mock->shouldReceive('getCNForEmployeeUser')->andReturn('test_connection');
        });

        // Mock Configuration to return null (no configuration exists)
        $this->mock(Configuration::class, function ($mock) {
            $mock->shouldReceive('on')->andReturnSelf();
            $mock->shouldReceive('where')->andReturnSelf();
            $mock->shouldReceive('first')->andReturn(null);
        });

        $result = $this->constraintService->getDateConstraints(EmployeeAttendanceDtl::ATTENDANCE_STATUS_OUT_WORK);

        $this->assertEquals('none', $result['type']);
        $this->assertNull($result['min_date']);
        $this->assertNull($result['max_date']);
        $this->assertEquals('', $result['message']);
    }

    /** @test */
    public function it_includes_existing_vacation_requests_in_constraints()
    {
        // Mock EmployeeAttendanceDtl
        $this->mock(EmployeeAttendanceDtl::class, function ($mock) {
            $mock->shouldReceive('getAttStatusId')
                ->with(EmployeeAttendanceDtl::ATTENDANCE_STATUS_VACATION)
                ->andReturn('ATTENDANCE_STATUS_VACATION');
        });

        // Mock ConnectionService
        $this->mock(ConnectionService::class, function ($mock) {
            $mock->shouldReceive('getCNForEmployeeUser')->andReturn('test_connection');
        });

        // Mock Configuration with both value and value1
        $config = new \stdClass();
        $config->config_id = 'ATTENDANCE_STATUS_VACATION';
        $config->value = 'anchor,employee_dtl.company_work_date,day,365';
        $config->value1 = 'anchor,now,day,30';

        $this->mock(Configuration::class, function ($mock) use ($config) {
            $mock->shouldReceive('on')->andReturnSelf();
            $mock->shouldReceive('where')
                ->with(Configuration::CONFIG_ID, 'ATTENDANCE_STATUS_VACATION')
                ->andReturnSelf();
            $mock->shouldReceive('first')->andReturn($config);
        });

        // Test that the method structure works (will return 'none' due to missing user/employee data)
        $result = $this->constraintService->getDateConstraints(EmployeeAttendanceDtl::ATTENDANCE_STATUS_VACATION);

        // The result will be 'none' because we can't mock the complex user/employee/request relationships
        // But this test verifies that the new logic doesn't break the existing flow
        $this->assertIsArray($result);
        $this->assertArrayHasKey('type', $result);
        $this->assertArrayHasKey('min_date', $result);
        $this->assertArrayHasKey('max_date', $result);
        $this->assertArrayHasKey('message', $result);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
    }
}
