<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Filament\Components\ChronoAnchorComponent;
use App\Filament\Components\ChronoRangeComponent;
use App\Filament\Components\ChronoCountComponent;

class ChronoComponentsParsingTest extends TestCase
{
    // ChronoAnchorComponent Tests
    
    /** @test */
    public function chrono_anchor_parses_custom_date_formula_correctly()
    {
        $formula = 'anchor,2024-01-15,day,5';
        $parsed = ChronoAnchorComponent::parseFormula($formula);

        $this->assertEquals('day', $parsed['type']);
        $this->assertEquals(5, $parsed['shift_count']);
        $this->assertEquals('custom', $parsed['date_source']);
        $this->assertEquals('2024-01-15', $parsed['custom_date']);
    }

    /** @test */
    public function chrono_anchor_parses_model_field_formula_correctly()
    {
        $formula = 'anchor,employee.created_at,month,-2';
        $parsed = ChronoAnchorComponent::parseFormula($formula);

        $this->assertEquals('month', $parsed['type']);
        $this->assertEquals(-2, $parsed['shift_count']);
        $this->assertEquals('employee.created_at', $parsed['date_source']);
        $this->assertNull($parsed['custom_date']);
    }

    /** @test */
    public function chrono_anchor_parses_complex_model_field_formula_correctly()
    {
        $formula = 'anchor,employee_dtl.company_work_date,year,1';
        $parsed = ChronoAnchorComponent::parseFormula($formula);

        $this->assertEquals('year', $parsed['type']);
        $this->assertEquals(1, $parsed['shift_count']);
        $this->assertEquals('employee_dtl.company_work_date', $parsed['date_source']);
        $this->assertNull($parsed['custom_date']);
    }


    
    /** @test */
    public function chrono_anchor_handles_invalid_formula_gracefully()
    {
        $formula = 'invalid-formula';
        $parsed = ChronoAnchorComponent::parseFormula($formula);
        
        $this->assertNull($parsed['type']);
        $this->assertNull($parsed['shift_count']);
        $this->assertEquals('custom', $parsed['date_source']);
        $this->assertNull($parsed['custom_date']);
    }
    
    // ChronoRangeComponent Tests
    
    /** @test */
    public function chrono_range_parses_formula_correctly()
    {
        $formula = 'range,day,5,10';
        $parsed = ChronoRangeComponent::parseFormula($formula);

        $this->assertEquals('day', $parsed['type']);
        $this->assertEquals(5, $parsed['from']);
        $this->assertEquals(10, $parsed['to']);
    }

    /** @test */
    public function chrono_range_parses_negative_values_correctly()
    {
        $formula = 'range,month,-3,2';
        $parsed = ChronoRangeComponent::parseFormula($formula);

        $this->assertEquals('month', $parsed['type']);
        $this->assertEquals(-3, $parsed['from']);
        $this->assertEquals(2, $parsed['to']);
    }


    
    /** @test */
    public function chrono_range_handles_invalid_formula_gracefully()
    {
        $formula = 'invalid-formula';
        $parsed = ChronoRangeComponent::parseFormula($formula);
        
        $this->assertNull($parsed['type']);
        $this->assertNull($parsed['from']);
        $this->assertNull($parsed['to']);
    }
    
    // ChronoCountComponent Tests
    
    /** @test */
    public function chrono_count_parses_formula_correctly()
    {
        $formula = 'count,day,10,month,5';
        $parsed = ChronoCountComponent::parseFormula($formula);

        $this->assertEquals('day', $parsed['static_type']);
        $this->assertEquals('month', $parsed['chrono_type']);
        $this->assertEquals(5, $parsed['length']);
        $this->assertEquals(10, $parsed['count']);
    }

    /** @test */
    public function chrono_count_parses_negative_values_correctly()
    {
        $formula = 'count,year,15,quarter,-2';
        $parsed = ChronoCountComponent::parseFormula($formula);

        $this->assertEquals('year', $parsed['static_type']);
        $this->assertEquals('quarter', $parsed['chrono_type']);
        $this->assertEquals(-2, $parsed['length']);
        $this->assertEquals(15, $parsed['count']);
    }


    
    /** @test */
    public function chrono_count_handles_invalid_formula_gracefully()
    {
        $formula = 'invalid-formula';
        $parsed = ChronoCountComponent::parseFormula($formula);
        
        $this->assertNull($parsed['static_type']);
        $this->assertNull($parsed['chrono_type']);
        $this->assertNull($parsed['length']);
        $this->assertNull($parsed['count']);
    }
}
