<?php

namespace App\Models\Customer\Time\RequestConfig;

use App\Models\EmployeeUser;
use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use App\Services\ConnectionService;
use App\Services\EmployeeUserService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RequestConfigDtl extends Model
{
    use HasFactory;

    const ID                = 'id';
    const REQUEST_CONFIG_ID = 'request_config_id';
    const LIMIT_HOURS       = 'limit_hours';
    const TYPE              = 'type';
    const EMPLOYEE_USER_ID  = 'employee_user_id';

    const RELATION_REQUEST_CONFIG = 'requestConfig';
    const RELATION_EMPLOYEE_USER  = 'employee_user';

    const TYPE_CHULUU         = 'chuluu';
    const TYPE_ILUU_TSAG      = 'iluu_tsag';
    const TYPE_GADUUR_AJILLAH = 'gaduur_ajillah';

    const TYPE_OPTIONS = [
        self::TYPE_CHULUU         => 'Чөлөө',
        self::TYPE_ILUU_TSAG      => 'Илүү цаг',
        self::TYPE_GADUUR_AJILLAH => 'Гадуур ажиллах',
    ];

    protected $fillable = [
        self::REQUEST_CONFIG_ID,
        self::LIMIT_HOURS,
        self::TYPE,
        self::EMPLOYEE_USER_ID
    ];

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    public function getEmployeeAttendanceStatusAttribute(): string
    {
        switch ($this->type) {
            case RequestConfigDtl::TYPE_CHULUU:
                $attStatus = EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH;
                break;
            case RequestConfigDtl::TYPE_ILUU_TSAG:
                $attStatus = EmployeeAttendanceDtl::ATTENDANCE_STATUS_OVER;
                break;
            case RequestConfigDtl::TYPE_GADUUR_AJILLAH: 
                $attStatus = EmployeeAttendanceDtl::ATTENDANCE_STATUS_WORK;
                break;
        }
        return $attStatus;
    }

    public function requestConfig()
    {
        return $this->belongsTo(RequestConfig::class);
    }

    public function employee_user()
    {
        return $this->belongsTo(EmployeeUser::class);
    }

    public function getEmployeeDepartmentNameAttribute(): string
    {
        $employeeUser = resolve(EmployeeUserService::class)->getEmployeeUser($this->employee_user_id);
        if (!isset($employeeUser))
            return '';
        return $employeeUser->getEmployeeDepartmentName();
    }

    public function getEmployeePositionNameAttribute(): string
    {
        $employeeUser = resolve(EmployeeUserService::class)->getEmployeeUser($this->employee_user_id);
        if (!isset($employeeUser))
            return '';
        return $employeeUser->getEmployeePositionName();
    }

    public function getEmployeeDisplayNameAttribute(): string
    {
        $employeeUser = resolve(EmployeeUserService::class)->getEmployeeUser($this->employee_user_id);
        if (!isset($employeeUser))
            return '';
        return $employeeUser->getEmployeeDisplayName();
    }
}
