#0 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /mnt/e/workspaces/astra/time/api/app/Models/Customer/Time/TimeRequest/TimeRequest.php(111): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#2 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php(23): App\\Models\\Customer\\Time\\TimeRequest\\TimeRequest->getAttStatusName()
#3 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php(155): Illuminate\\Http\\Resources\\Json\\JsonResource->forwardCallTo()
#4 /mnt/e/workspaces/astra/time/api/app/Http/Resources/TimeRequest.php(35): Illuminate\\Http\\Resources\\Json\\JsonResource->__call()
#5 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php(108): App\\Http\\Resources\\TimeRequest->toArray()
#6 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php(39): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve()
#7 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php(245): Illuminate\\Http\\Resources\\Json\\ResourceResponse->toResponse()
#8 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(900): Illuminate\\Http\\Resources\\Json\\JsonResource->toResponse()
#9 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(885): Illuminate\\Routing\\Router::toResponse()
#10 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Routing\\Router->prepareResponse()
#11 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#12 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#14 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#16 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#17 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#18 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#20 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#22 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#23 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#24 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#25 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#26 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#27 /mnt/e/workspaces/astra/time/api/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#29 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#31 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#32 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#34 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#35 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#37 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#39 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#41 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#43 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#45 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#46 /mnt/e/workspaces/astra/time/api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#47 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('...')
#48 {main}
"} 
[2025-08-26 12:12:36] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-26 12:12:36] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Configuration loaded {"config_found":true,"config_value":"count,year,hour,4,1","config_value1":"anchor,now,day,1"} 
[2025-08-26 12:12:36] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Start {"anchorFormula":"anchor,now,day,1"} 
[2025-08-26 12:12:36] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Formula parsed {"anchorData":{"type":"day","shift_count":1,"date_source":"now","custom_date":null}} 
[2025-08-26 12:12:36] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - End {"result":"2025-08-27 12:12:36"} 
[2025-08-26 12:12:36] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Start {"countFormula":"count,year,hour,4,1","attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-26 12:12:36] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Processing {"chronoType":"year","count":1} 
[2025-08-26 12:12:36] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Period calculated {"start":"2025-01-01","end":"2025-12-31"} 
[2025-08-26 12:12:36] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Existing requests counted {"existingRequestsCount":0,"countLimit":1} 
[2025-08-26 12:12:36] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Count limit OK  
[2025-08-26 12:12:36] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Count limit check {"countLimitValid":true,"message":""} 
[2025-08-26 12:12:36] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Start {"countFormula":"count,year,hour,4,1","attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY","countLimitValid":true} 
[2025-08-26 12:12:36] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Processing {"chronoType":"year","now":"2025-08-26 12:12:36"} 
[2025-08-26 12:12:36] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Using previous session {"previousSessionDate":"2024-08-26","sessionEndDate":"2024-12-31"} 
[2025-08-26 12:12:36] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - End {"result":"2025-01-01 00:00:00"} 
[2025-08-26 12:12:36] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Min dates calculated {"min_dates_count":2,"min_dates":["2025-08-27","2025-01-01"]} 
[2025-08-26 12:12:37] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - End {"result":{"type":"min_date","min_date":"2025-08-27","max_date":null,"message":"Salary furlough requests must be after 2025-08-27"},"max_min_date":"2025-08-27"} 
[2025-08-26 12:13:13] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-26 12:13:13] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Configuration loaded {"config_found":true,"config_value":"count,year,hour,4,1","config_value1":"anchor,now,day,1"} 
[2025-08-26 12:13:13] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Start {"anchorFormula":"anchor,now,day,1"} 
[2025-08-26 12:13:13] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Formula parsed {"anchorData":{"type":"day","shift_count":1,"date_source":"now","custom_date":null}} 
[2025-08-26 12:13:13] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - End {"result":"2025-08-27 12:13:13"} 
[2025-08-26 12:13:13] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Start {"countFormula":"count,year,hour,4,1","attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-26 12:13:13] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Processing {"chronoType":"year","count":1} 
[2025-08-26 12:13:13] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Period calculated {"start":"2025-01-01","end":"2025-12-31"} 
[2025-08-26 12:13:13] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Existing requests counted {"existingRequestsCount":1,"countLimit":1} 
[2025-08-26 12:13:13] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Count limit exceeded {"message":"Цалинтай чөлөө (Төрсөн өдөр)-ийн хүсэлтийн тоо одоогийн жилийн хугацаанд (2025-01-01 - 2025-12-31) хязгаарлагдсан тооноос (1) хэтэрсэн байна. Одоогийн тоо: 1"} 
[2025-08-26 12:13:13] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Count limit check {"countLimitValid":false,"message":"Цалинтай чөлөө (Төрсөн өдөр)-ийн хүсэлтийн тоо одоогийн жилийн хугацаанд (2025-01-01 - 2025-12-31) хязгаарлагдсан тооноос (1) хэтэрсэн байна. Одоогийн тоо: 1"} 
[2025-08-26 12:13:13] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Start {"countFormula":"count,year,hour,4,1","attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY","countLimitValid":false} 
[2025-08-26 12:13:13] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Processing {"chronoType":"year","now":"2025-08-26 12:13:13"} 
[2025-08-26 12:13:13] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Using current session {"sessionEndDate":"2025-12-31"} 
[2025-08-26 12:13:13] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - End {"result":"2026-01-01 00:00:00"} 
[2025-08-26 12:13:13] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Min dates calculated {"min_dates_count":2,"min_dates":["2025-08-27","2026-01-01"]} 
[2025-08-26 12:13:13] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - End {"result":{"type":"min_date","min_date":"2026-01-01","max_date":null,"message":"Salary furlough requests must be after 2026-01-01"},"max_min_date":"2026-01-01"} 
[2025-08-26 12:14:44] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-26 12:14:44] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Configuration loaded {"config_found":true,"config_value":"count,year,hour,4,1","config_value1":"anchor,now,day,1"} 
[2025-08-26 12:14:44] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Start {"anchorFormula":"anchor,now,day,1"} 
[2025-08-26 12:14:44] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Formula parsed {"anchorData":{"type":"day","shift_count":1,"date_source":"now","custom_date":null}} 
[2025-08-26 12:14:44] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - End {"result":"2025-08-27 12:14:44"} 
[2025-08-26 12:14:44] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Start {"countFormula":"count,year,hour,4,1","attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-26 12:14:44] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Processing {"chronoType":"year","count":1} 
[2025-08-26 12:14:44] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Period calculated {"start":"2025-01-01","end":"2025-12-31"} 
[2025-08-26 12:14:44] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Existing requests counted {"existingRequestsCount":1,"countLimit":1} 
[2025-08-26 12:14:44] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Count limit exceeded {"message":"Цалинтай чөлөө (Төрсөн өдөр)-ийн хүсэлтийн тоо одоогийн жилийн хугацаанд (2025-01-01 - 2025-12-31) хязгаарлагдсан тооноос (1) хэтэрсэн байна. Одоогийн тоо: 1"} 
[2025-08-26 12:14:44] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Count limit check {"countLimitValid":false,"message":"Цалинтай чөлөө (Төрсөн өдөр)-ийн хүсэлтийн тоо одоогийн жилийн хугацаанд (2025-01-01 - 2025-12-31) хязгаарлагдсан тооноос (1) хэтэрсэн байна. Одоогийн тоо: 1"} 
[2025-08-26 12:14:44] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Start {"countFormula":"count,year,hour,4,1","attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY","countLimitValid":false} 
[2025-08-26 12:14:44] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Processing {"chronoType":"year","now":"2025-08-26 12:14:44"} 
[2025-08-26 12:14:44] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Using current session {"sessionEndDate":"2025-12-31"} 
[2025-08-26 12:14:44] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - End {"result":"2026-01-01 00:00:00"} 
[2025-08-26 12:14:44] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Min dates calculated {"min_dates_count":2,"min_dates":["2025-08-27","2026-01-01"]} 
[2025-08-26 12:14:44] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - End {"result":{"type":"min_date","min_date":"2026-01-01","max_date":null,"message":"Salary furlough requests must be after 2026-01-01"},"max_min_date":"2026-01-01"} 
[2025-08-26 12:19:03] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-26 12:19:03] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Configuration loaded {"config_found":true,"config_value":"count,halfyear,hour,4,1","config_value1":null} 
[2025-08-26 12:19:03] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Start {"countFormula":"count,halfyear,hour,4,1","attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-26 12:19:03] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Processing {"chronoType":"halfyear","count":1} 
[2025-08-26 12:19:03] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Period calculated {"start":"2025-07-01","end":"2025-12-31"} 
[2025-08-26 12:19:03] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Existing requests counted {"existingRequestsCount":1,"countLimit":1} 
[2025-08-26 12:19:03] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Count limit exceeded {"message":"Цалинтай чөлөө (Төрсөн өдөр)-ийн хүсэлтийн тоо одоогийн хагас жилийн хугацаанд (2025-07-01 - 2025-12-31) хязгаарлагдсан тооноос (1) хэтэрсэн байна. Одоогийн тоо: 1"} 
[2025-08-26 12:19:03] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Count limit check {"countLimitValid":false,"message":"Цалинтай чөлөө (Төрсөн өдөр)-ийн хүсэлтийн тоо одоогийн хагас жилийн хугацаанд (2025-07-01 - 2025-12-31) хязгаарлагдсан тооноос (1) хэтэрсэн байна. Одоогийн тоо: 1"} 
[2025-08-26 12:19:03] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Start {"countFormula":"count,halfyear,hour,4,1","attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY","countLimitValid":false} 
[2025-08-26 12:19:03] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Processing {"chronoType":"halfyear","now":"2025-08-26 12:19:03"} 
[2025-08-26 12:19:03] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Using current session {"sessionEndDate":"2025-12-31"} 
[2025-08-26 12:19:03] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - End {"result":"2026-01-01 00:00:00"} 
[2025-08-26 12:19:03] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Min dates calculated {"min_dates_count":1,"min_dates":["2026-01-01"]} 
[2025-08-26 12:19:03] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - End {"result":{"type":"min_date","min_date":"2026-01-01","max_date":null,"message":"Salary furlough requests must be after 2026-01-01"},"max_min_date":"2026-01-01"} 
[2025-08-26 12:19:27] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-26 12:19:28] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Configuration loaded {"config_found":true,"config_value":"count,halfyear,hour,4,1","config_value1":null} 
[2025-08-26 12:19:28] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Start {"countFormula":"count,halfyear,hour,4,1","attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-26 12:19:28] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Processing {"chronoType":"halfyear","count":1} 
[2025-08-26 12:19:28] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Period calculated {"start":"2025-07-01","end":"2025-12-31"} 
[2025-08-26 12:19:28] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Existing requests counted {"existingRequestsCount":0,"countLimit":1} 
[2025-08-26 12:19:28] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Count limit OK  
[2025-08-26 12:19:28] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Count limit check {"countLimitValid":true,"message":""} 
[2025-08-26 12:19:28] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Start {"countFormula":"count,halfyear,hour,4,1","attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY","countLimitValid":true} 
[2025-08-26 12:19:28] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Processing {"chronoType":"halfyear","now":"2025-08-26 12:19:28"} 
[2025-08-26 12:19:28] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Using previous session {"previousSessionDate":"2025-02-26","sessionEndDate":"2025-06-30"} 
[2025-08-26 12:19:28] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - End {"result":"2025-07-01 00:00:00"} 
[2025-08-26 12:19:28] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Min dates calculated {"min_dates_count":1,"min_dates":["2025-07-01"]} 
[2025-08-26 12:19:28] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - End {"result":{"type":"min_date","min_date":"2025-07-01","max_date":null,"message":"Salary furlough requests must be after 2025-07-01"},"max_min_date":"2025-07-01"} 
[2025-08-26 12:22:23] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-26 12:22:23] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Configuration loaded {"config_found":true,"config_value":"count,halfyear,hour,4,1","config_value1":"anchor,now,day,1"} 
[2025-08-26 12:22:23] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Start {"anchorFormula":"anchor,now,day,1"} 
[2025-08-26 12:22:23] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Formula parsed {"anchorData":{"type":"day","shift_count":1,"date_source":"now","custom_date":null}} 
[2025-08-26 12:22:23] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - End {"result":"2025-08-27 12:22:23"} 
[2025-08-26 12:22:23] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Start {"countFormula":"count,halfyear,hour,4,1","attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-26 12:22:23] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Processing {"chronoType":"halfyear","count":1} 
[2025-08-26 12:22:23] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Period calculated {"start":"2025-07-01","end":"2025-12-31"} 
[2025-08-26 12:22:23] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Existing requests counted {"existingRequestsCount":0,"countLimit":1} 
[2025-08-26 12:22:23] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Count limit OK  
[2025-08-26 12:22:23] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Count limit check {"countLimitValid":true,"message":""} 
[2025-08-26 12:22:23] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Start {"countFormula":"count,halfyear,hour,4,1","attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY","countLimitValid":true} 
[2025-08-26 12:22:23] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Processing {"chronoType":"halfyear","now":"2025-08-26 12:22:23"} 
[2025-08-26 12:22:23] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Using previous session {"previousSessionDate":"2025-02-26","sessionEndDate":"2025-06-30"} 
[2025-08-26 12:22:23] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - End {"result":"2025-07-01 00:00:00"} 
[2025-08-26 12:22:23] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Min dates calculated {"min_dates_count":2,"min_dates":["2025-08-27","2025-07-01"]} 
[2025-08-26 12:22:23] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - End {"result":{"type":"min_date","min_date":"2025-08-27","max_date":null,"message":"Salary furlough requests must be after 2025-08-27"},"max_min_date":"2025-08-27"} 
[2025-08-26 12:23:33] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-26 12:23:33] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Configuration loaded {"config_found":true,"config_value":"count,halfyear,hour,4,1","config_value1":"anchor,now,day,1"} 
[2025-08-26 12:23:33] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Start {"anchorFormula":"anchor,now,day,1"} 
[2025-08-26 12:23:33] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Formula parsed {"anchorData":{"type":"day","shift_count":1,"date_source":"now","custom_date":null}} 
[2025-08-26 12:23:33] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - End {"result":"2025-08-27 12:23:33"} 
[2025-08-26 12:23:33] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Start {"countFormula":"count,halfyear,hour,4,1","attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-26 12:23:33] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Processing {"chronoType":"halfyear","count":1} 
[2025-08-26 12:23:33] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Period calculated {"start":"2025-07-01","end":"2025-12-31"} 
[2025-08-26 12:23:33] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Existing requests counted {"existingRequestsCount":1,"countLimit":1} 
[2025-08-26 12:23:33] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Count limit exceeded {"message":"Цалинтай чөлөө (Төрсөн өдөр)-ийн хүсэлтийн тоо одоогийн хагас жилийн хугацаанд (2025-07-01 - 2025-12-31) хязгаарлагдсан тооноос (1) хэтэрсэн байна. Одоогийн тоо: 1"} 
[2025-08-26 12:23:33] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Count limit check {"countLimitValid":false,"message":"Цалинтай чөлөө (Төрсөн өдөр)-ийн хүсэлтийн тоо одоогийн хагас жилийн хугацаанд (2025-07-01 - 2025-12-31) хязгаарлагдсан тооноос (1) хэтэрсэн байна. Одоогийн тоо: 1"} 
[2025-08-26 12:23:33] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Start {"countFormula":"count,halfyear,hour,4,1","attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY","countLimitValid":false} 
[2025-08-26 12:23:33] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Processing {"chronoType":"halfyear","now":"2025-08-26 12:23:33"} 
[2025-08-26 12:23:33] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Using current session {"sessionEndDate":"2025-12-31"} 
[2025-08-26 12:23:33] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - End {"result":"2026-01-01 00:00:00"} 
[2025-08-26 12:23:33] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Min dates calculated {"min_dates_count":2,"min_dates":["2025-08-27","2026-01-01"]} 
[2025-08-26 12:23:33] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - End {"result":{"type":"min_date","min_date":"2026-01-01","max_date":null,"message":"Salary furlough requests must be after 2026-01-01"},"max_min_date":"2026-01-01"} 
[2025-08-27 11:08:40] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'updated_by' in 'field list' (Connection: tm_astraCentral, SQL: update `configurations` set `value` = count,halfyear,1,day,2, `value1` = ?, `updated_by` = 12, `configurations`.`updated_at` = 2025-08-27 11:08:39 where `id` = 25) {"userId":12,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'updated_by' in 'field list' (Connection: tm_astraCentral, SQL: update `configurations` set `value` = count,halfyear,1,day,2, `value1` = ?, `updated_by` = 12, `configurations`.`updated_at` = 2025-08-27 11:08:39 where `id` = 25) at /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#1 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(600): Illuminate\\Database\\Connection->run()
#2 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(552): Illuminate\\Database\\Connection->affectingStatement()
#3 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3602): Illuminate\\Database\\Connection->update()
#4 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1061): Illuminate\\Database\\Query\\Builder->update()
#5 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1214): Illuminate\\Database\\Eloquent\\Builder->update()
#6 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1131): Illuminate\\Database\\Eloquent\\Model->performUpdate()
#7 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(991): Illuminate\\Database\\Eloquent\\Model->save()
#8 /mnt/e/workspaces/astra/time/api/vendor/filament/filament/src/Resources/Pages/EditRecord.php(251): Illuminate\\Database\\Eloquent\\Model->update()
#9 /mnt/e/workspaces/astra/time/api/app/Filament/Resources/Admin/ConfigurationResource/Pages/EditConfiguration.php(110): Filament\\Resources\\Pages\\EditRecord->handleRecordUpdate()
#10 /mnt/e/workspaces/astra/time/api/vendor/filament/filament/src/Resources/Pages/EditRecord.php(151): App\\Filament\\Resources\\Admin\\ConfigurationResource\\Pages\\EditConfiguration->handleRecordUpdate()
#11 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Filament\\Resources\\Pages\\EditRecord->save()
#12 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#14 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#15 /mnt/e/workspaces/astra/time/api/vendor/livewire/livewire/src/Wrapped.php(23): Illuminate\\Container\\BoundMethod::call()
#16 /mnt/e/workspaces/astra/time/api/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(474): Livewire\\Wrapped->__call()
#17 /mnt/e/workspaces/astra/time/api/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(101): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->callMethods()
#18 /mnt/e/workspaces/astra/time/api/vendor/livewire/livewire/src/LivewireManager.php(97): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update()
#19 /mnt/e/workspaces/astra/time/api/vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php(94): Livewire\\LivewireManager->update()
#20 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#21 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#22 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#23 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#24 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#25 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#29 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#31 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#33 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#34 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#36 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#38 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#40 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#41 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#42 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#43 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#44 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#45 /mnt/e/workspaces/astra/time/api/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#47 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#49 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#53 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#61 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 /mnt/e/workspaces/astra/time/api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('...')
#64 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'updated_by' in 'field list' at /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Connection.php:608)
[stacktrace]
#0 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(608): PDO->prepare()
#1 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#3 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(600): Illuminate\\Database\\Connection->run()
#4 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(552): Illuminate\\Database\\Connection->affectingStatement()
#5 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3602): Illuminate\\Database\\Connection->update()
#6 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1061): Illuminate\\Database\\Query\\Builder->update()
#7 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1214): Illuminate\\Database\\Eloquent\\Builder->update()
#8 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1131): Illuminate\\Database\\Eloquent\\Model->performUpdate()
#9 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(991): Illuminate\\Database\\Eloquent\\Model->save()
#10 /mnt/e/workspaces/astra/time/api/vendor/filament/filament/src/Resources/Pages/EditRecord.php(251): Illuminate\\Database\\Eloquent\\Model->update()
#11 /mnt/e/workspaces/astra/time/api/app/Filament/Resources/Admin/ConfigurationResource/Pages/EditConfiguration.php(110): Filament\\Resources\\Pages\\EditRecord->handleRecordUpdate()
#12 /mnt/e/workspaces/astra/time/api/vendor/filament/filament/src/Resources/Pages/EditRecord.php(151): App\\Filament\\Resources\\Admin\\ConfigurationResource\\Pages\\EditConfiguration->handleRecordUpdate()
#13 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Filament\\Resources\\Pages\\EditRecord->save()
#14 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#16 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 /mnt/e/workspaces/astra/time/api/vendor/livewire/livewire/src/Wrapped.php(23): Illuminate\\Container\\BoundMethod::call()
#18 /mnt/e/workspaces/astra/time/api/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(474): Livewire\\Wrapped->__call()
#19 /mnt/e/workspaces/astra/time/api/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(101): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->callMethods()
#20 /mnt/e/workspaces/astra/time/api/vendor/livewire/livewire/src/LivewireManager.php(97): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update()
#21 /mnt/e/workspaces/astra/time/api/vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php(94): Livewire\\LivewireManager->update()
#22 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#23 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#24 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#25 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#26 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#27 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#29 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#31 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#33 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#35 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#36 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#38 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#40 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#42 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#43 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#44 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#45 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#46 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#47 /mnt/e/workspaces/astra/time/api/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#49 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#51 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#53 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#55 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#57 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#59 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#61 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#63 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#64 /mnt/e/workspaces/astra/time/api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#65 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('...')
#66 {main}
"} 
[2025-08-27 11:10:23] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'value1' in 'configurations' (Connection: mysql, SQL: alter table `configurations` add `created_by` bigint unsigned null after `value1`, add `updated_by` bigint unsigned null after `created_by`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'value1' in 'configurations' (Connection: mysql, SQL: alter table `configurations` add `created_by` bigint unsigned null after `value1`, add `updated_by` bigint unsigned null after `created_by`) at E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('alter table `co...', Array, Object(Closure))
#1 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('alter table `co...', Array, Object(Closure))
#2 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('alter table `co...')
#3 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(444): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->table('configurations', Object(Closure))
#6 E:\\workspaces\\astra\\time\\api\\database\\migrations\\customer\\2025_08_27_000001_add_created_by_updated_by_to_configurations_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#7 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render('2025_08_27_0000...', Object(Closure))
#13 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_08_27_0000...', Object(Closure))
#14 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('E:\\\\workspaces\\\\a...', 12, false)
#15 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 E:\\workspaces\\astra\\time\\api\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'value1' in 'configurations' at E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `co...', Array)
#2 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('alter table `co...', Array, Object(Closure))
#3 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('alter table `co...', Array, Object(Closure))
#4 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('alter table `co...')
#5 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(444): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->table('configurations', Object(Closure))
#8 E:\\workspaces\\astra\\time\\api\\database\\migrations\\customer\\2025_08_27_000001_add_created_by_updated_by_to_configurations_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#9 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render('2025_08_27_0000...', Object(Closure))
#15 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_08_27_0000...', Object(Closure))
#16 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('E:\\\\workspaces\\\\a...', 12, false)
#17 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 E:\\workspaces\\astra\\time\\api\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 {main}
"} 
[2025-08-27 11:10:43] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1142 REFERENCES command denied to user 'time'@'DESKTOP-A7TRQ1M.mshome.net' for table 'prod_time_v1.departments' (Connection: mysql, SQL: alter table `positions` add constraint `positions_department_id_foreign` foreign key (`department_id`) references `departments` (`id`)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1142 REFERENCES command denied to user 'time'@'DESKTOP-A7TRQ1M.mshome.net' for table 'prod_time_v1.departments' (Connection: mysql, SQL: alter table `positions` add constraint `positions_department_id_foreign` foreign key (`department_id`) references `departments` (`id`)) at E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('alter table `po...', Array, Object(Closure))
#1 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('alter table `po...', Array, Object(Closure))
#2 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('alter table `po...')
#3 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('positions', Object(Closure))
#6 E:\\workspaces\\astra\\time\\api\\database\\migrations\\customer\\2024_01_11_035738_create_positions_table.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): CreatePositionsTable->up()
#8 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(CreatePositionsTable), 'up')
#9 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(CreatePositionsTable), 'up')
#11 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render('2024_01_11_0357...', Object(Closure))
#13 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_11_0357...', Object(Closure))
#14 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('E:\\\\workspaces\\\\a...', 12, false)
#15 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 E:\\workspaces\\astra\\time\\api\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1142 REFERENCES command denied to user 'time'@'DESKTOP-A7TRQ1M.mshome.net' for table 'prod_time_v1.departments' at E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `po...', Array)
#2 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('alter table `po...', Array, Object(Closure))
#3 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('alter table `po...', Array, Object(Closure))
#4 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('alter table `po...')
#5 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('positions', Object(Closure))
#8 E:\\workspaces\\astra\\time\\api\\database\\migrations\\customer\\2024_01_11_035738_create_positions_table.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): CreatePositionsTable->up()
#10 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(CreatePositionsTable), 'up')
#11 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(CreatePositionsTable), 'up')
#13 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render('2024_01_11_0357...', Object(Closure))
#15 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_11_0357...', Object(Closure))
#16 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('E:\\\\workspaces\\\\a...', 12, false)
#17 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 E:\\workspaces\\astra\\time\\api\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 {main}
"} 
[2025-08-27 11:11:05] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'configurations_config_id_unique' (Connection: mysql, SQL: alter table `configurations` add unique `configurations_config_id_unique`(`config_id`)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'configurations_config_id_unique' (Connection: mysql, SQL: alter table `configurations` add unique `configurations_config_id_unique`(`config_id`)) at E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('alter table `co...', Array, Object(Closure))
#1 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('alter table `co...', Array, Object(Closure))
#2 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('alter table `co...')
#3 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(444): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->table('configurations', Object(Closure))
#6 E:\\workspaces\\astra\\time\\api\\database\\migrations\\customer\\2025_08_19_131558_add_unique_constraint_to_config_id_in_configurations_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#7 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render('2025_08_19_1315...', Object(Closure))
#13 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_08_19_1315...', Object(Closure))
#14 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('E:\\\\workspaces\\\\a...', 14, false)
#15 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 E:\\workspaces\\astra\\time\\api\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'configurations_config_id_unique' at E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `co...', Array)
#2 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('alter table `co...', Array, Object(Closure))
#3 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('alter table `co...', Array, Object(Closure))
#4 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('alter table `co...')
#5 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(444): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->table('configurations', Object(Closure))
#8 E:\\workspaces\\astra\\time\\api\\database\\migrations\\customer\\2025_08_19_131558_add_unique_constraint_to_config_id_in_configurations_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#9 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render('2025_08_19_1315...', Object(Closure))
#15 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_08_19_1315...', Object(Closure))
#16 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('E:\\\\workspaces\\\\a...', 14, false)
#17 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 E:\\workspaces\\astra\\time\\api\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 {main}
"} 
[2025-08-27 11:11:17] local.ERROR: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'value' (Connection: mysql, SQL: alter table `configurations` add `value` varchar(255) null) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S21): SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'value' (Connection: mysql, SQL: alter table `configurations` add `value` varchar(255) null) at E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('alter table `co...', Array, Object(Closure))
#1 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('alter table `co...', Array, Object(Closure))
#2 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('alter table `co...')
#3 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(444): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->table('configurations', Object(Closure))
#6 E:\\workspaces\\astra\\time\\api\\database\\migrations\\customer\\2025_08_21_154938_make_value_nullable_in_configurations_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#7 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render('2025_08_21_1549...', Object(Closure))
#13 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_08_21_1549...', Object(Closure))
#14 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('E:\\\\workspaces\\\\a...', 14, false)
#15 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 E:\\workspaces\\astra\\time\\api\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}

[previous exception] [object] (PDOException(code: 42S21): SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'value' at E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `co...', Array)
#2 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('alter table `co...', Array, Object(Closure))
#3 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('alter table `co...', Array, Object(Closure))
#4 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('alter table `co...')
#5 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(444): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->table('configurations', Object(Closure))
#8 E:\\workspaces\\astra\\time\\api\\database\\migrations\\customer\\2025_08_21_154938_make_value_nullable_in_configurations_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#9 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render('2025_08_21_1549...', Object(Closure))
#15 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_08_21_1549...', Object(Closure))
#16 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('E:\\\\workspaces\\\\a...', 14, false)
#17 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 E:\\workspaces\\astra\\time\\api\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 {main}
"} 
[2025-08-27 12:06:55] local.INFO: Form data before save: {"config_id":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE","value":"count,halfyear,1,day,2","salary_furlough_count_static_type":"halfyear","salary_furlough_count_count":1,"salary_furlough_count_chrono_type":"day","salary_furlough_count_length":"2","salary_furlough_count_formula":"count,halfyear,1,day,2","value1":"anchor,now,day,3","anchor_config_type":"day","anchor_config_shift_count":3,"anchor_config_date_source":"now","anchor_config_formula":"anchor,now,day,3"} 
[2025-08-27 12:06:55] local.INFO: Original record values: {"value":"count,halfyear,1,day,1","value1":"anchor,now,day,3","config_id":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE"} 
[2025-08-27 12:07:21] local.INFO: Form data before save: {"config_id":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE","value":"count,halfyear,1,day,1","salary_furlough_count_static_type":"halfyear","salary_furlough_count_count":1,"salary_furlough_count_chrono_type":"day","salary_furlough_count_length":"1","salary_furlough_count_formula":"count,halfyear,1,day,1","value1":"anchor,now,day,3","anchor_config_type":"day","anchor_config_shift_count":3,"anchor_config_date_source":"now","anchor_config_formula":"anchor,now,day,3"} 
[2025-08-27 12:07:21] local.INFO: Original record values: {"value":"count,halfyear,1,day,2","value1":"anchor,now,day,3","config_id":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE"} 
[2025-08-27 13:33:22] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-27 13:33:22] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Configuration loaded {"config_found":true,"config_value":"count,halfyear,1,hour,4","config_value1":"anchor,now,day,1"} 
[2025-08-27 13:33:22] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Start {"anchorFormula":"anchor,now,day,1"} 
[2025-08-27 13:33:22] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Formula parsed {"anchorData":{"type":"day","shift_count":1,"date_source":"now","custom_date":null}} 
[2025-08-27 13:33:22] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - End {"result":"2025-08-28 13:33:22"} 
[2025-08-27 13:33:22] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Start {"countFormula":"count,halfyear,1,hour,4","attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-27 13:33:22] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Processing {"chronoType":"halfyear","count":1} 
[2025-08-27 13:33:22] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Period calculated {"start":"2025-07-01","end":"2025-12-31"} 
[2025-08-27 13:33:22] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Existing requests counted {"existingRequestsCount":1,"countLimit":1} 
[2025-08-27 13:33:22] local.INFO: TimeRequestDateConstraintService::validateCountLimitForConstraints - Count limit exceeded {"message":"Цалинтай чөлөө (Төрсөн өдөр)-ийн хүсэлтийн тоо одоогийн хагас жилийн хугацаанд (2025-07-01 - 2025-12-31) хязгаарлагдсан тооноос (1) хэтэрсэн байна. Одоогийн тоо: 1"} 
[2025-08-27 13:33:22] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Count limit check {"countLimitValid":false,"message":"Цалинтай чөлөө (Төрсөн өдөр)-ийн хүсэлтийн тоо одоогийн хагас жилийн хугацаанд (2025-07-01 - 2025-12-31) хязгаарлагдсан тооноос (1) хэтэрсэн байна. Одоогийн тоо: 1"} 
[2025-08-27 13:33:22] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Start {"countFormula":"count,halfyear,1,hour,4","attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY","countLimitValid":false} 
[2025-08-27 13:33:22] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Processing {"chronoType":"halfyear","now":"2025-08-27 13:33:22"} 
[2025-08-27 13:33:22] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - Using current session {"sessionEndDate":"2025-12-31"} 
[2025-08-27 13:33:22] local.INFO: TimeRequestDateConstraintService::calculateSessionMinDate - End {"result":"2026-01-01 00:00:00"} 
[2025-08-27 13:33:22] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Min dates calculated {"min_dates_count":2,"min_dates":["2025-08-28","2026-01-01"]} 
[2025-08-27 13:33:22] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - End {"result":{"type":"min_date","min_date":"2026-01-01","max_date":null,"message":"Salary furlough requests must be after 2026-01-01"},"max_min_date":"2026-01-01"} 
[2025-08-27 13:34:31] local.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_OUT_WORK"} 
[2025-08-27 13:34:31] local.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Configuration loaded {"config_found":true,"config_value1":"anchor,now,hour,1"} 
[2025-08-27 13:34:31] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Start {"anchorFormula":"anchor,now,hour,1"} 
[2025-08-27 13:34:31] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Formula parsed {"anchorData":{"type":"hour","shift_count":1,"date_source":"now","custom_date":null}} 
[2025-08-27 13:34:31] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - End {"result":"2025-08-27 14:34:31"} 
[2025-08-27 13:34:31] local.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Min date calculated {"min_date":"2025-08-27"} 
[2025-08-27 13:34:31] local.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - End {"result":{"type":"min_date","min_date":"2025-08-27","max_date":null,"message":"Requests must be after 2025-08-27"}} 
[2025-08-27 13:52:29] local.ERROR: PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR on line 6 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR on line 6 at E:\\workspaces\\astra\\time\\api\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 E:\\workspaces\\astra\\time\\api\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 E:\\workspaces\\astra\\time\\api\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php \\nuse App\\\\...', false)
#2 E:\\workspaces\\astra\\time\\api\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 E:\\workspaces\\astra\\time\\api\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('\\nuse App\\\\Servic...', true)
#4 E:\\workspaces\\astra\\time\\api\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('\\nuse App\\\\Servic...', true)
#5 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('\\nuse App\\\\Servic...')
#6 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#12 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 E:\\workspaces\\astra\\time\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 E:\\workspaces\\astra\\time\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 E:\\workspaces\\astra\\time\\api\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 {main}
"} 
[2025-08-27 13:54:22] local.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_OUT_WORK"} 
[2025-08-27 13:54:22] local.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 13:54:44] local.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_OUT_WORK"} 
[2025-08-27 13:54:44] local.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 13:54:44] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_VACATION"} 
[2025-08-27 13:54:44] local.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 13:56:57] testing.ERROR: TimeRequestValidationService error: Attempt to read property "organization" on null  
[2025-08-27 13:56:57] testing.ERROR: TimeRequestValidationService error: Attempt to read property "organization" on null  
[2025-08-27 13:56:57] testing.ERROR: TimeRequestValidationService error: Attempt to read property "organization" on null  
[2025-08-27 13:56:58] testing.ERROR: TimeRequestValidationService error: Attempt to read property "organization" on null  
[2025-08-27 13:56:58] testing.ERROR: TimeRequestValidationService error: Attempt to read property "organization" on null  
[2025-08-27 13:56:58] testing.ERROR: TimeRequestValidationService validateValue error: Attempt to read property "organization" on null  
[2025-08-27 13:56:58] testing.INFO: TimeRequestValidationService::validateCountLimit - Start {"beginDate":"2024-02-15","count":3,"chronoType":"month","attStatus":91} 
[2025-08-27 13:56:58] testing.INFO: TimeRequestValidationService::validateCountLimit - Period dates calculated {"periodDates":{"start":"2024-02-01","end":"2024-02-29"}} 
[2025-08-27 13:56:58] testing.ERROR: Error validating count limit: Attempt to read property "organization" on null  
[2025-08-27 13:56:58] testing.ERROR: TimeRequestValidationService error: Attempt to read property "organization" on null  
[2025-08-27 13:56:58] testing.ERROR: TimeRequestValidationService error: Attempt to read property "organization" on null  
[2025-08-27 13:56:58] testing.ERROR: TimeRequestValidationService validateValue error: Attempt to read property "organization" on null  
[2025-08-27 13:56:58] testing.ERROR: Error validating against existing vacation requests: Database connection [test_cn] not configured.  
[2025-08-27 13:56:58] testing.ERROR: Error validating vacation with company work date: Attempt to read property "organization" on null  
[2025-08-27 13:57:33] testing.ERROR: TimeRequestValidationService error: Attempt to read property "organization" on null  
[2025-08-27 13:59:01] testing.ERROR: TimeRequestValidationService error: Attempt to read property "organization" on null  
[2025-08-27 13:59:21] testing.ERROR: TimeRequestValidationService error: Attempt to read property "organization" on null  
[2025-08-27 13:59:53] testing.ERROR: TimeRequestValidationService error: Attempt to read property "organization" on null  
[2025-08-27 14:08:27] testing.ERROR: TimeRequestValidationService error: Attempt to read property "organization" on null  
[2025-08-27 14:09:15] testing.INFO: TimeRequestValidationService::validateValue1 - Debug {"attStatus":3,"attStatusId":"ATTENDANCE_STATUS_WORK"} 
[2025-08-27 14:09:15] testing.ERROR: TimeRequestValidationService error: Attempt to read property "organization" on null  
[2025-08-27 14:10:59] testing.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_VACATION"} 
[2025-08-27 14:10:59] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:10:59] testing.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_OUT_WORK"} 
[2025-08-27 14:10:59] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:10:59] testing.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_FURLOUGH_LONG"} 
[2025-08-27 14:10:59] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:10:59] testing.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_VACATION"} 
[2025-08-27 14:10:59] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:10:59] testing.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-27 14:10:59] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:10:59] testing.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_OUT_WORK"} 
[2025-08-27 14:10:59] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:22:20] testing.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_VACATION"} 
[2025-08-27 14:22:20] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:22:21] testing.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_OUT_WORK"} 
[2025-08-27 14:22:21] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:22:21] testing.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_FURLOUGH_LONG"} 
[2025-08-27 14:22:21] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:22:21] testing.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_VACATION"} 
[2025-08-27 14:22:21] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:22:21] testing.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-27 14:22:21] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:22:21] testing.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_OUT_WORK"} 
[2025-08-27 14:22:21] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:22:51] testing.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_OUT_WORK"} 
[2025-08-27 14:22:51] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:24:30] local.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_OUT_WORK"} 
[2025-08-27 14:24:30] local.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Configuration loaded {"config_found":true,"config_value1":"anchor,now,hour,1"} 
[2025-08-27 14:24:30] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Start {"anchorFormula":"anchor,now,hour,1"} 
[2025-08-27 14:24:30] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Formula parsed {"anchorData":{"type":"hour","shift_count":1,"date_source":"now","custom_date":null}} 
[2025-08-27 14:24:30] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - End {"result":"2025-08-27 15:24:30","chrono_type":"hour"} 
[2025-08-27 14:24:30] local.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Min date calculated {"min_date":"2025-08-27"} 
[2025-08-27 14:24:30] local.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - End {"result":{"type":"min_date","min_date":"2025-08-27 15:24","max_date":null,"message":"Requests must be after 2025-08-27 15:24"}} 
[2025-08-27 14:26:11] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_VACATION"} 
[2025-08-27 14:26:11] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Configuration loaded {"config_found":true,"config_value":"anchor,employee_dtl.company_work_date,month,6","config_value1":"anchor,now,day,10"} 
[2025-08-27 14:26:11] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Start {"anchorFormula":"anchor,employee_dtl.company_work_date,month,6"} 
[2025-08-27 14:26:11] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Formula parsed {"anchorData":{"type":"month","shift_count":6,"date_source":"employee_dtl.company_work_date","custom_date":null}} 
[2025-08-27 14:26:11] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - User info {"user_id":107,"user_phone":"94013949"} 
[2025-08-27 14:26:11] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Employee info {"employee_found":true,"employee_id":96,"has_employee_dtl":true,"company_work_date":"2025-02-07"} 
[2025-08-27 14:26:11] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Company work date parsed {"companyWorkDate":"2025-02-07 00:00:00","shift_type":"month","shift_count":6} 
[2025-08-27 14:26:11] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - End {"result":"2025-08-07 00:00:00"} 
[2025-08-27 14:26:11] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Start {"anchorFormula":"anchor,now,day,10"} 
[2025-08-27 14:26:11] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Formula parsed {"anchorData":{"type":"day","shift_count":10,"date_source":"now","custom_date":null}} 
[2025-08-27 14:26:11] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - End {"result":"2025-09-06 14:26:11","chrono_type":"day"} 
[2025-08-27 14:26:11] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - End {"result":{"type":"min_date","min_date":"2025-09-06","max_date":null,"message":"Vacation requests must be after 2025-09-06"},"min_dates_count":2,"max_min_date":"2025-09-06"} 
[2025-08-27 14:28:03] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_VACATION"} 
[2025-08-27 14:28:03] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Configuration loaded {"config_found":true,"config_value":"anchor,employee_dtl.company_work_date,month,6","config_value1":"anchor,now,day,10"} 
[2025-08-27 14:28:03] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Start {"anchorFormula":"anchor,employee_dtl.company_work_date,month,6"} 
[2025-08-27 14:28:03] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Formula parsed {"anchorData":{"type":"month","shift_count":6,"date_source":"employee_dtl.company_work_date","custom_date":null}} 
[2025-08-27 14:28:03] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - User info {"user_id":107,"user_phone":"94013949"} 
[2025-08-27 14:28:03] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Employee info {"employee_found":true,"employee_id":96,"has_employee_dtl":true,"company_work_date":"2025-04-07"} 
[2025-08-27 14:28:03] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Company work date parsed {"companyWorkDate":"2025-04-07 00:00:00","shift_type":"month","shift_count":6} 
[2025-08-27 14:28:03] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - End {"result":"2025-10-07 00:00:00"} 
[2025-08-27 14:28:03] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Start {"anchorFormula":"anchor,now,day,10"} 
[2025-08-27 14:28:03] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Formula parsed {"anchorData":{"type":"day","shift_count":10,"date_source":"now","custom_date":null}} 
[2025-08-27 14:28:03] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - End {"result":"2025-09-06 14:28:03","chrono_type":"day"} 
[2025-08-27 14:28:03] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - End {"result":{"type":"min_date","min_date":"2025-10-07","max_date":null,"message":"Vacation requests must be after 2025-10-07"},"min_dates_count":2,"max_min_date":"2025-10-07"} 
[2025-08-27 14:28:31] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_VACATION"} 
[2025-08-27 14:28:31] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Configuration loaded {"config_found":true,"config_value":"anchor,employee_dtl.company_work_date,month,6","config_value1":"anchor,now,day,10"} 
[2025-08-27 14:28:31] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Start {"anchorFormula":"anchor,employee_dtl.company_work_date,month,6"} 
[2025-08-27 14:28:31] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Formula parsed {"anchorData":{"type":"month","shift_count":6,"date_source":"employee_dtl.company_work_date","custom_date":null}} 
[2025-08-27 14:28:31] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - User info {"user_id":107,"user_phone":"94013949"} 
[2025-08-27 14:28:31] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Employee info {"employee_found":true,"employee_id":96,"has_employee_dtl":true,"company_work_date":"2025-02-07"} 
[2025-08-27 14:28:31] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Company work date parsed {"companyWorkDate":"2025-02-07 00:00:00","shift_type":"month","shift_count":6} 
[2025-08-27 14:28:31] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - End {"result":"2025-08-07 00:00:00"} 
[2025-08-27 14:28:31] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Start {"anchorFormula":"anchor,now,day,10"} 
[2025-08-27 14:28:31] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Formula parsed {"anchorData":{"type":"day","shift_count":10,"date_source":"now","custom_date":null}} 
[2025-08-27 14:28:31] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - End {"result":"2025-09-06 14:28:31","chrono_type":"day"} 
[2025-08-27 14:28:31] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - End {"result":{"type":"min_date","min_date":"2025-09-06","max_date":null,"message":"Vacation requests must be after 2025-09-06"},"min_dates_count":2,"max_min_date":"2025-09-06"} 
[2025-08-27 14:31:06] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_VACATION"} 
[2025-08-27 14:31:06] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Configuration loaded {"config_found":true,"config_value":"anchor,employee_dtl.company_work_date,month,6","config_value1":"anchor,now,day,10"} 
[2025-08-27 14:31:06] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Start {"anchorFormula":"anchor,employee_dtl.company_work_date,month,6"} 
[2025-08-27 14:31:06] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Formula parsed {"anchorData":{"type":"month","shift_count":6,"date_source":"employee_dtl.company_work_date","custom_date":null}} 
[2025-08-27 14:31:06] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - User info {"user_id":107,"user_phone":"94013949"} 
[2025-08-27 14:31:06] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Employee info {"employee_found":true,"employee_id":96,"has_employee_dtl":true,"company_work_date":"2025-02-07"} 
[2025-08-27 14:31:06] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Company work date parsed {"companyWorkDate":"2025-02-07 00:00:00","shift_type":"month","shift_count":6} 
[2025-08-27 14:31:06] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - End {"result":"2025-08-07 00:00:00"} 
[2025-08-27 14:31:06] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Start {"anchorFormula":"anchor,now,day,10"} 
[2025-08-27 14:31:06] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Formula parsed {"anchorData":{"type":"day","shift_count":10,"date_source":"now","custom_date":null}} 
[2025-08-27 14:31:06] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - End {"result":"2025-09-06 14:31:06","chrono_type":"day"} 
[2025-08-27 14:31:06] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - End {"result":{"type":"min_date","min_date":"2025-09-06","max_date":null,"message":"Vacation requests must be after 2025-09-06"},"min_dates_count":2,"max_min_date":"2025-09-06"} 
[2025-08-27 14:31:35] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_VACATION"} 
[2025-08-27 14:31:35] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Configuration loaded {"config_found":true,"config_value":"anchor,employee_dtl.company_work_date,month,6","config_value1":"anchor,now,day,10"} 
[2025-08-27 14:31:35] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Start {"anchorFormula":"anchor,employee_dtl.company_work_date,month,6"} 
[2025-08-27 14:31:35] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Formula parsed {"anchorData":{"type":"month","shift_count":6,"date_source":"employee_dtl.company_work_date","custom_date":null}} 
[2025-08-27 14:31:35] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - User info {"user_id":107,"user_phone":"94013949"} 
[2025-08-27 14:31:35] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Employee info {"employee_found":true,"employee_id":96,"has_employee_dtl":true,"company_work_date":"2025-02-07"} 
[2025-08-27 14:31:35] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Company work date parsed {"companyWorkDate":"2025-02-07 00:00:00","shift_type":"month","shift_count":6} 
[2025-08-27 14:31:35] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - End {"result":"2025-08-07 00:00:00"} 
[2025-08-27 14:31:35] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Start {"anchorFormula":"anchor,now,day,10"} 
[2025-08-27 14:31:35] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Formula parsed {"anchorData":{"type":"day","shift_count":10,"date_source":"now","custom_date":null}} 
[2025-08-27 14:31:35] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - End {"result":"2025-09-06 14:31:35","chrono_type":"day"} 
[2025-08-27 14:31:35] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - End {"result":{"type":"min_date","min_date":"2025-09-06","max_date":null,"message":"Vacation requests must be after 2025-09-06"},"min_dates_count":2,"max_min_date":"2025-09-06"} 
[2025-08-27 14:42:09] local.INFO: TimeRequestDateConstraintService::calculateExistingVacationRequestsMin - Start {"anchorFormula":"anchor,employee_dtl.company_work_date,day,365"} 
[2025-08-27 14:42:09] local.WARNING: TimeRequestDateConstraintService::calculateExistingVacationRequestsMin - No authenticated user  
[2025-08-27 14:44:41] testing.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_VACATION"} 
[2025-08-27 14:44:41] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:44:52] testing.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_VACATION"} 
[2025-08-27 14:44:52] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:44:52] testing.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_OUT_WORK"} 
[2025-08-27 14:44:52] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:44:52] testing.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_FURLOUGH_LONG"} 
[2025-08-27 14:44:52] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:44:52] testing.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_VACATION"} 
[2025-08-27 14:44:52] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:44:52] testing.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-27 14:44:52] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:44:52] testing.INFO: TimeRequestDateConstraintService::getValue1DateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_OUT_WORK"} 
[2025-08-27 14:44:52] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:44:52] testing.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_VACATION"} 
[2025-08-27 14:44:52] testing.ERROR: TimeRequestDateConstraintService error: Attempt to read property "organization" on null  
[2025-08-27 14:45:24] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_VACATION"} 
[2025-08-27 14:45:24] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Configuration loaded {"config_found":true,"config_value":"anchor,employee_dtl.company_work_date,month,6","config_value1":"anchor,now,day,10"} 
[2025-08-27 14:45:24] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Start {"anchorFormula":"anchor,employee_dtl.company_work_date,month,6"} 
[2025-08-27 14:45:24] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Formula parsed {"anchorData":{"type":"month","shift_count":6,"date_source":"employee_dtl.company_work_date","custom_date":null}} 
[2025-08-27 14:45:24] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - User info {"user_id":107,"user_phone":"94013949"} 
[2025-08-27 14:45:24] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Employee info {"employee_found":true,"employee_id":96,"has_employee_dtl":true,"company_work_date":"2025-02-07"} 
[2025-08-27 14:45:24] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Company work date parsed {"companyWorkDate":"2025-02-07 00:00:00","shift_type":"month","shift_count":6} 
[2025-08-27 14:45:24] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - End {"result":"2025-08-07 00:00:00"} 
[2025-08-27 14:45:24] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Start {"anchorFormula":"anchor,now,day,10"} 
[2025-08-27 14:45:24] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Formula parsed {"anchorData":{"type":"day","shift_count":10,"date_source":"now","custom_date":null}} 
[2025-08-27 14:45:24] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - End {"result":"2025-09-06 14:45:24","chrono_type":"day"} 
[2025-08-27 14:45:24] local.INFO: TimeRequestDateConstraintService::calculateExistingVacationRequestsMin - Start {"anchorFormula":"anchor,employee_dtl.company_work_date,month,6"} 
[2025-08-27 14:45:24] local.INFO: TimeRequestDateConstraintService::calculateExistingVacationRequestsMin - Existing requests found {"count":1} 
[2025-08-27 14:45:24] local.INFO: TimeRequestDateConstraintService::calculateExistingVacationRequestsMin - Request processed {"request_id":325,"end_date":"2025-04-29","shifted_date":"2025-10-29 00:00:00"} 
[2025-08-27 14:45:24] local.INFO: TimeRequestDateConstraintService::calculateExistingVacationRequestsMin - End {"min_dates_count":1} 
[2025-08-27 14:45:24] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - End {"result":{"type":"min_date","min_date":"2025-10-29","max_date":null,"message":"Vacation requests must be after 2025-10-29"},"min_dates_count":3,"max_min_date":"2025-10-29","includes_existing_requests":true} 
[2025-08-27 14:46:24] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_VACATION"} 
[2025-08-27 14:46:24] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - Configuration loaded {"config_found":true,"config_value":"anchor,employee_dtl.company_work_date,month,6","config_value1":"anchor,now,day,10"} 
[2025-08-27 14:46:24] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Start {"anchorFormula":"anchor,employee_dtl.company_work_date,month,6"} 
[2025-08-27 14:46:24] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Formula parsed {"anchorData":{"type":"month","shift_count":6,"date_source":"employee_dtl.company_work_date","custom_date":null}} 
[2025-08-27 14:46:24] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - User info {"user_id":107,"user_phone":"94013949"} 
[2025-08-27 14:46:24] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Employee info {"employee_found":true,"employee_id":96,"has_employee_dtl":true,"company_work_date":"2025-02-07"} 
[2025-08-27 14:46:24] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - Company work date parsed {"companyWorkDate":"2025-02-07 00:00:00","shift_type":"month","shift_count":6} 
[2025-08-27 14:46:24] local.INFO: TimeRequestDateConstraintService::calculateVacationCompanyWorkDateMin - End {"result":"2025-08-07 00:00:00"} 
[2025-08-27 14:46:24] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Start {"anchorFormula":"anchor,now,day,10"} 
[2025-08-27 14:46:24] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Formula parsed {"anchorData":{"type":"day","shift_count":10,"date_source":"now","custom_date":null}} 
[2025-08-27 14:46:24] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - End {"result":"2025-09-06 14:46:24","chrono_type":"day"} 
[2025-08-27 14:46:24] local.INFO: TimeRequestDateConstraintService::calculateExistingVacationRequestsMin - Start {"anchorFormula":"anchor,employee_dtl.company_work_date,month,6"} 
[2025-08-27 14:46:24] local.INFO: TimeRequestDateConstraintService::calculateExistingVacationRequestsMin - Existing requests found {"count":1} 
[2025-08-27 14:46:24] local.INFO: TimeRequestDateConstraintService::calculateExistingVacationRequestsMin - Request processed {"request_id":325,"end_date":"2024-04-29","shifted_date":"2024-10-29 00:00:00"} 
[2025-08-27 14:46:24] local.INFO: TimeRequestDateConstraintService::calculateExistingVacationRequestsMin - End {"min_dates_count":1} 
[2025-08-27 14:46:24] local.INFO: TimeRequestDateConstraintService::getVacationDateConstraints - End {"result":{"type":"min_date","min_date":"2025-09-06","max_date":null,"message":"Vacation requests must be after 2025-09-06"},"min_dates_count":3,"max_min_date":"2025-09-06","includes_existing_requests":true} 
