<?php

namespace App\Filament\Components;

use Filament\Forms;

class ChronoRangeComponent
{
    public static function make(string $name = 'chrono_range'): Forms\Components\Group
    {
        return Forms\Components\Group::make([
            Forms\Components\Select::make("{$name}_type")
                ->label('Chrono Type')
                ->options([
                    'minute' => 'Minute',
                    'hour' => 'Hour',
                    'halfday' => 'Half Day',
                    'day' => 'Day',
                    'month' => 'Month',
                    'quarter' => 'Quarter',
                    'halfyear' => 'Half Year',
                    'year' => 'Year'
                ])
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) => 
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\TextInput::make("{$name}_from")
                ->label('From')
                ->numeric()
                ->minValue(0)
                ->rules(['integer', 'min:0'])
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) =>
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\TextInput::make("{$name}_to")
                ->label('To')
                ->numeric()
                ->minValue(0)
                ->rules(['integer', 'min:0'])
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) =>
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\Hidden::make("{$name}_formula")
        ])->columns(3);
    }

    private static function updateFormula(string $name, callable $set, callable $get): void
    {
        $type = $get("{$name}_type");
        $from = $get("{$name}_from");
        $to = $get("{$name}_to");

        if ($type && $from !== null && $from !== '' && $to !== null && $to !== '') {
            // New format: range,type,from,to
            $formula = "range,{$type},{$from},{$to}";
            $set("{$name}_formula", $formula);
        }
    }

    /**
     * Parse a formula string back into component values
     * Formula format: "range,type,from,to"
     */
    public static function parseFormula(string $formula): array
    {
        $result = [
            'type' => null,
            'from' => null,
            'to' => null,
        ];

        // Split by comma and check if it's the new format
        $parts = explode(',', $formula);

        if (count($parts) === 4 && $parts[0] === 'range') {
            $type = $parts[1];
            $from = (int)$parts[2];
            $to = (int)$parts[3];

            $result['type'] = $type;
            $result['from'] = $from;
            $result['to'] = $to;
        }

        return $result;
    }
}